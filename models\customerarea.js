'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class CustomerArea extends Model {
    static associate(models) {
      CustomerArea.belongsTo(models.Customer, {
        foreignKey: 'customer_id',
        as: 'customer'
      });
    }
  }
  
  CustomerArea.init({
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Customers',
        key: 'id'
      }
    },
    address: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'CustomerArea',
    tableName: 'CustomerAreas',
    indexes: [
      {
        unique: true,
        fields: ['customer_id']  // ✅ تأكد أنها array
      }
    ]
  });
  
  return CustomerArea;
};
