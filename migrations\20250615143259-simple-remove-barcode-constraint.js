'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    try {
      // محاولة حذف القيد المحدد
      await queryInterface.sequelize.query(`
        IF EXISTS (SELECT * FROM sys.key_constraints WHERE name = 'UQ__Products__C16E36F8DD4F40C1')
        BEGIN
          ALTER TABLE Products DROP CONSTRAINT UQ__Products__C16E36F8DD4F40C1;
        END
      `);
    } catch (error) {
      console.log('Constraint may not exist:', error.message);
    }
  },

  async down (queryInterface, Sequelize) {
    // لا نحتاج لإعادة إضافة القيد
  }
};
