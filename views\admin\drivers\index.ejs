<div class="container mt-4">
  <h3 class="mb-3">مندوبي التوصيل</h3>
  <a href="/admin/drivers/create" class="btn btn-primary mb-3">إضافة مندوب توصيل</a>

  <div class="card">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-bordered text-center align-middle">
          <thead class="table-light">
            <tr>
              <th>الصورة</th>
              <th>الاسم</th>
              <th>رقم الهاتف</th>
              <th>عدد التوصيلات</th>
              <th>الحالة</th>
              <th>الإجراءات</th>
            </tr>
          </thead>
          <tbody>
            <% if (deliveryPeople.length > 0) { %>
              <% deliveryPeople.forEach(person => { %>
                <tr>
                  <td>
                    <% if (person.image) { %>
                      <img src="<%= person.image %>" alt="صورة" class="rounded-circle" width="50" height="50">
                    <% } else { %>
                      <span class="text-muted">لا توجد صورة</span>
                    <% } %>
                  </td>
                  <td><%= person.name %></td>
                  <td><%= person.phoneNumber %></td>
                  <td>
                    <a href="/admin/drivers/<%= person.id %>/deliveries">
                      <%= person.deliveries ? person.deliveries.length : 0 %>
                    </a>
                  </td>
                  <td>
                    <% if (person.status === 'active') { %>
                      <span class="badge bg-success">نشط</span>
                    <% } else { %>
                      <span class="badge bg-secondary">غير نشط</span>
                    <% } %>
                  </td>
                  <td>
                    <a href="/admin/deliveries/create?deliveryPersonId=<%= person.id %>" class="btn btn-success btn-sm ms-1">
                      إضافة توصيل
                    </a>
                    <a href="/admin/drivers/<%= person.id %>/edit" class="btn btn-warning btn-sm">تعديل</a>
                    <form action="/admin/drivers/<%= person.id %>/delete" method="POST" class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا المندوب؟')">
                      <button type="submit" class="btn btn-danger btn-sm">حذف</button>
                    </form>
                  </td>
                </tr>
              <% }) %>
            <% } else { %>
              <tr>
                <td colspan="6" class="text-muted">لا يوجد مندوبي توصيل</td>
              </tr>
            <% } %>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <% if (totalPages > 1) { %>
    <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
      <ul class="pagination">
        <% if (currentPage > 1) { %>
          <li class="page-item">
            <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
          </li>
        <% } else { %>
          <li class="page-item disabled">
            <span class="page-link">السابق</span>
          </li>
        <% } %>

        <% for(let i = 1; i <= totalPages; i++) { %>
          <li class="page-item <%= currentPage === i ? 'active' : '' %>">
            <a class="page-link" href="?page=<%= i %>"><%= i %></a>
          </li>
        <% } %>

        <% if (currentPage < totalPages) { %>
          <li class="page-item">
            <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
          </li>
        <% } else { %>
          <li class="page-item disabled">
            <span class="page-link">التالي</span>
          </li>
        <% } %>
      </ul>
    </nav>
  <% } %>
</div>
