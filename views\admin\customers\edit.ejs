<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>تعديل العميل: <%= customer.name %></h1>
        <a href="/admin/customers" class="btn btn-secondary">العودة للعملاء</a>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <%= error %>
        </div>
    <% } %>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form action="/admin/customers/<%= customer.id %>" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<%= customer.name %>"
                                           required placeholder="أدخل اسم العميل">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phoneNumber" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber"
                                           value="<%= customer.phoneNumber || '' %>"
                                           required placeholder="أدخل رقم الهاتف">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="barcode" class="form-label">الباركود</label>
                                    <input type="text" class="form-control" id="barcode" name="barcode"
                                           value="<%= customer.barcode || '' %>"
                                           placeholder="أدخل الباركود">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discountRate" class="form-label">نسبة الخصم (%)</label>
                                    <input type="number" class="form-control" id="discountRate" name="discountRate"
                                           value="<%= customer.discountRate || 0 %>"
                                           min="0" max="100" step="0.01" placeholder="أدخل نسبة الخصم">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <%= customer.status === 'active' ? 'selected' : '' %>>نشط</option>
                                        <option value="inactive" <%= customer.status === 'inactive' ? 'selected' : '' %>>غير نشط</option>
                                        <option value="pending" <%= customer.status === 'pending' ? 'selected' : '' %>>في الانتظار</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="image" class="form-label">صورة العميل</label>
                                    <input type="file" class="form-control" id="image" name="image"
                                           accept="image/*">
                                    <% if (customer.image) { %>
                                        <div class="mt-2">
                                            <img src="/uploads/<%= customer.image %>" alt="صورة العميل"
                                                 style="max-width: 100px; height: auto; border-radius: 8px;">
                                            <small class="d-block text-muted">الصورة الحالية</small>
                                        </div>
                                    <% } %>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <div class="input-group">
                                        <textarea class="form-control" id="address" name="address" rows="3"
                                                  placeholder="أدخل عنوان العميل أو اختر من الخريطة"><%= customer.address || '' %></textarea>
                                        <button type="button" class="btn btn-outline-primary" id="selectFromMap">
                                            <i class="fas fa-map-marker-alt"></i> اختر من الخريطة
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">خط العرض (Latitude)</label>
                                    <input type="number" class="form-control" id="latitude" name="latitude"
                                           step="0.00000001" min="-90" max="90" readonly
                                           value="<%= customer.latitude || '' %>"
                                           placeholder="سيتم ملؤه تلقائياً من الخريطة">
                                    <small class="form-text text-muted">سيتم ملؤه تلقائياً عند اختيار الموقع من الخريطة</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">خط الطول (Longitude)</label>
                                    <input type="number" class="form-control" id="longitude" name="longitude"
                                           step="0.00000001" min="-180" max="180" readonly
                                           value="<%= customer.longitude || '' %>"
                                           placeholder="سيتم ملؤه تلقائياً من الخريطة">
                                    <small class="form-text text-muted">سيتم ملؤه تلقائياً عند اختيار الموقع من الخريطة</small>
                                </div>
                            </div>

                            <!-- خريطة لاختيار الموقع -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <div id="mapContainer" style="display: none;">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">اختر موقع العميل</h6>
                                                <button type="button" class="btn btn-sm btn-secondary" id="closeMap">إغلاق</button>
                                            </div>
                                            <div class="card-body">
                                                <div id="map" style="height: 400px; width: 100%;"></div>
                                                <div class="mt-3">
                                                    <button type="button" class="btn btn-success" id="confirmLocation">إغلاق الخريطة</button>
                                                    <button type="button" class="btn btn-secondary" id="getCurrentLocation">موقعي الحالي</button>
                                                    <small class="form-text text-muted d-block mt-2">
                                                        💡 انقر على الخريطة أو اسحب العلامة لتحديد الموقع - سيتم ملء العنوان والإحداثيات تلقائياً
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="permissions" class="form-label">الصلاحيات</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" id="permissions0" name="permissions" value="0"
                                               <%= customer.permissions == 0 ? 'checked' : '' %>>
                                        <label class="form-check-label" for="permissions0">
                                            <span class="badge bg-danger">غير مفعل</span> - لا يمكنه الوصول للنظام
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" id="permissions1" name="permissions" value="1"
                                               <%= customer.permissions == 1 ? 'checked' : '' %>>
                                        <label class="form-check-label" for="permissions1">
                                            <span class="badge bg-success">مفعل</span> - يمكنه الوصول للنظام والتطبيق
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4"
                                              placeholder="أدخل أي ملاحظات حول العميل"><%= customer.notes || '' %></textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="password" name="password"
                                           placeholder="اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور">
                                    <small class="form-text text-muted">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-3">
                            <a href="/admin/customers" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">تحديث العميل</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إضافة Leaflet Maps (مجاني) -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let map;
let marker;
let selectedLocation = null;

document.addEventListener('DOMContentLoaded', function() {
    // إظهار/إخفاء الخريطة
    document.getElementById('selectFromMap').addEventListener('click', function() {
        document.getElementById('mapContainer').style.display = 'block';
        setTimeout(() => {
            initMap();
        }, 100);
    });

    document.getElementById('closeMap').addEventListener('click', function() {
        document.getElementById('mapContainer').style.display = 'none';
    });

    // تأكيد الموقع المختار وإغلاق الخريطة
    document.getElementById('confirmLocation').addEventListener('click', function() {
        if (selectedLocation) {
            document.getElementById('mapContainer').style.display = 'none';
        } else {
            alert('يرجى اختيار موقع على الخريطة أولاً');
        }
    });

    // الحصول على الموقع الحالي
    document.getElementById('getCurrentLocation').addEventListener('click', function() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                selectedLocation = { lat: lat, lng: lng };

                // تحديث الخريطة
                map.setView([lat, lng], 15);
                marker.setLatLng([lat, lng]);

                // ملء الحقول تلقائياً
                updateLocationFields(lat, lng);

            }, function() {
                alert('لا يمكن الحصول على موقعك الحالي');
            });
        } else {
            alert('المتصفح لا يدعم خدمة تحديد الموقع');
        }
    });
});

function initMap() {
    // استخدام الموقع الحالي للعميل إذا كان متوفراً
    const currentLat = parseFloat(document.getElementById('latitude').value) || 24.7136;
    const currentLng = parseFloat(document.getElementById('longitude').value) || 46.6753;
    const defaultLocation = [currentLat, currentLng];

    // إنشاء الخريطة
    map = L.map('map').setView(defaultLocation, 15);

    // إضافة طبقة الخريطة
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // إنشاء العلامة
    marker = L.marker(defaultLocation, {
        draggable: true
    }).addTo(map);

    selectedLocation = { lat: currentLat, lng: currentLng };

    // عند النقر على الخريطة
    map.on('click', function(e) {
        selectedLocation = {
            lat: e.latlng.lat,
            lng: e.latlng.lng
        };
        marker.setLatLng([selectedLocation.lat, selectedLocation.lng]);

        // ملء الحقول تلقائياً
        updateLocationFields(selectedLocation.lat, selectedLocation.lng);
    });

    // عند سحب العلامة
    marker.on('dragend', function(e) {
        selectedLocation = {
            lat: e.target.getLatLng().lat,
            lng: e.target.getLatLng().lng
        };

        // ملء الحقول تلقائياً
        updateLocationFields(selectedLocation.lat, selectedLocation.lng);
    });
}

// دالة لتحديث حقول الموقع والعنوان
function updateLocationFields(lat, lng) {
    // تحديث حقول الإحداثيات
    document.getElementById('latitude').value = lat;
    document.getElementById('longitude').value = lng;

    // الحصول على العنوان وتحديثه
    getAddressFromCoordinates(lat, lng);
}

function getAddressFromCoordinates(lat, lng) {
    // استخدام خدمة Nominatim للحصول على العنوان
    fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&accept-language=ar`)
        .then(response => response.json())
        .then(data => {
            if (data && data.display_name) {
                document.getElementById('address').value = data.display_name;
            }
        })
        .catch(error => {
            console.log('Geocoding failed:', error);
        });
}
</script>
