<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>تعديل العميل: <%= customer.name %></h1>
        <a href="/admin/customers" class="btn btn-secondary">العودة للعملاء</a>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <%= error %>
        </div>
    <% } %>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form action="/admin/customers/<%= customer.id %>" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<%= customer.name %>"
                                           required placeholder="أدخل اسم العميل">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phoneNumber" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber"
                                           value="<%= customer.phoneNumber || '' %>"
                                           required placeholder="أدخل رقم الهاتف">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="barcode" class="form-label">الباركود</label>
                                    <input type="text" class="form-control" id="barcode" name="barcode"
                                           value="<%= customer.barcode || '' %>"
                                           placeholder="أدخل الباركود">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discountRate" class="form-label">نسبة الخصم (%)</label>
                                    <input type="number" class="form-control" id="discountRate" name="discountRate"
                                           value="<%= customer.discountRate || 0 %>"
                                           min="0" max="100" step="0.01" placeholder="أدخل نسبة الخصم">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <%= customer.status === 'active' ? 'selected' : '' %>>نشط</option>
                                        <option value="inactive" <%= customer.status === 'inactive' ? 'selected' : '' %>>غير نشط</option>
                                        <option value="pending" <%= customer.status === 'pending' ? 'selected' : '' %>>في الانتظار</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="image" class="form-label">صورة العميل</label>
                                    <input type="file" class="form-control" id="image" name="image"
                                           accept="image/*">
                                    <% if (customer.image) { %>
                                        <div class="mt-2">
                                            <img src="/uploads/<%= customer.image %>" alt="صورة العميل"
                                                 style="max-width: 100px; height: auto; border-radius: 8px;">
                                            <small class="d-block text-muted">الصورة الحالية</small>
                                        </div>
                                    <% } %>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" name="address" rows="3"
                                              placeholder="أدخل عنوان العميل"><%= customer.address || '' %></textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">خط العرض (Latitude)</label>
                                    <input type="number" class="form-control" id="latitude" name="latitude"
                                           step="0.00000001" min="-90" max="90"
                                           value="<%= customer.latitude || '' %>"
                                           placeholder="مثال: 24.7136">
                                    <small class="form-text text-muted">اختياري - يمكن الحصول عليه من الخريطة</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">خط الطول (Longitude)</label>
                                    <input type="number" class="form-control" id="longitude" name="longitude"
                                           step="0.00000001" min="-180" max="180"
                                           value="<%= customer.longitude || '' %>"
                                           placeholder="مثال: 46.6753">
                                    <small class="form-text text-muted">اختياري - يمكن الحصول عليه من الخريطة</small>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="permissions" class="form-label">الصلاحيات</label>
                                    <%
                                        let customerPermissions = [];
                                        try {
                                            customerPermissions = customer.permissions ? (typeof customer.permissions === 'string' ? JSON.parse(customer.permissions) : customer.permissions) : [];
                                        } catch (e) {
                                            customerPermissions = [];
                                        }
                                    %>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="canOrder" name="permissions[]" value="canOrder"
                                                       <%= customerPermissions.includes('canOrder') ? 'checked' : '' %>>
                                                <label class="form-check-label" for="canOrder">
                                                    يمكنه تقديم الطلبات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="canViewPrices" name="permissions[]" value="canViewPrices"
                                                       <%= customerPermissions.includes('canViewPrices') ? 'checked' : '' %>>
                                                <label class="form-check-label" for="canViewPrices">
                                                    يمكنه رؤية الأسعار
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="canViewHistory" name="permissions[]" value="canViewHistory"
                                                       <%= customerPermissions.includes('canViewHistory') ? 'checked' : '' %>>
                                                <label class="form-check-label" for="canViewHistory">
                                                    يمكنه رؤية تاريخ الطلبات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="canReceiveNotifications" name="permissions[]" value="canReceiveNotifications"
                                                       <%= customerPermissions.includes('canReceiveNotifications') ? 'checked' : '' %>>
                                                <label class="form-check-label" for="canReceiveNotifications">
                                                    يمكنه استقبال الإشعارات
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="canUseDiscount" name="permissions[]" value="canUseDiscount"
                                                       <%= customerPermissions.includes('canUseDiscount') ? 'checked' : '' %>>
                                                <label class="form-check-label" for="canUseDiscount">
                                                    يمكنه استخدام الخصم
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="canCancelOrder" name="permissions[]" value="canCancelOrder"
                                                       <%= customerPermissions.includes('canCancelOrder') ? 'checked' : '' %>>
                                                <label class="form-check-label" for="canCancelOrder">
                                                    يمكنه إلغاء الطلبات
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4"
                                              placeholder="أدخل أي ملاحظات حول العميل"><%= customer.notes || '' %></textarea>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور الجديدة</label>
                                    <input type="password" class="form-control" id="password" name="password"
                                           placeholder="اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور">
                                    <small class="form-text text-muted">اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-3">
                            <a href="/admin/customers" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">تحديث العميل</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
