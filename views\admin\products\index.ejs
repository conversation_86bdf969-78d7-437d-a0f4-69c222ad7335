<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات - نظام إدارة المتجر</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #B2CD9C;
            --secondary-color: #8FBC8F;
            --accent-color: #7BA05B;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--bg-light) 0%, #e8f5e8 100%);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.2rem;
        }

        .nav-link:hover {
            color: var(--white) !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white) !important;
        }

        .container-main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .page-header {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            border-left: 5px solid var(--primary-color);
        }

        .product-card {
            background: var(--white);
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
            border-top: 3px solid var(--primary-color);
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .product-image {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            object-fit: cover;
            border: 2px solid var(--bg-light);
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: var(--white);
        }

        .badge-custom {
            background: var(--primary-color);
            color: var(--white);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
        }

        .price-tag {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .stock-badge {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }

        .action-buttons .btn {
            margin: 0 0.2rem;
            border-radius: 8px;
        }

        @media (max-width: 768px) {
            .container-main {
                padding: 1rem;
            }
            
            .product-card {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-store me-2"></i>
                نظام إدارة المتجر
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/customers">
                            <i class="fas fa-users me-1"></i>العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/products">
                            <i class="fas fa-box me-1"></i>المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/categories">
                            <i class="fas fa-tags me-1"></i>الفئات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders">
                            <i class="fas fa-shopping-cart me-1"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/deliveries">
                            <i class="fas fa-truck me-1"></i>التوصيلات
                        </a>
                    </li>
                </ul>
                
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        المدير
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/admin/profile">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/settings">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/admin/auth/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-main">
        <!-- Page Header -->
        <div class="page-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-2" style="color: var(--primary-color);">
                        <i class="fas fa-box me-2"></i>
                        إدارة المنتجات
                    </h1>
                    <p class="mb-0" style="color: var(--text-light);">
                        <i class="fas fa-info-circle me-2"></i>
                        إدارة وتنظيم جميع منتجات المتجر
                    </p>
                </div>
                <div>
                    <a href="/admin/products/create" class="btn btn-custom">
                        <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                    </a>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="row">
            <% if (products && products.length > 0) { %>
                <% products.forEach(product => { %>
                    <div class="col-md-6 col-lg-4">
                        <div class="product-card">
                            <div class="d-flex align-items-start">
                                <div class="me-3">
                                    <% if (product.images && product.images.length > 0) { %>
                                        <img src="<%= product.images[0].path %>" alt="<%= product.name %>" class="product-image">
                                    <% } else { %>
                                        <div class="product-image d-flex align-items-center justify-content-center" style="background: var(--bg-light);">
                                            <i class="fas fa-image text-muted"></i>
                                        </div>
                                    <% } %>
                                </div>
                                <div class="flex-grow-1">
                                    <h5 class="mb-2" style="color: var(--text-dark);"><%= product.name %></h5>
                                    <p class="text-muted mb-2 small"><%= product.description || 'لا يوجد وصف' %></p>
                                    
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <span class="price-tag">
                                            <i class="fas fa-tag me-1"></i>
                                            <%= product.price %> ر.س
                                        </span>
                                        <span class="stock-badge">
                                            <i class="fas fa-boxes me-1"></i>
                                            <%= product.quantity %> قطعة
                                        </span>
                                    </div>
                                    
                                    <% if (product.category) { %>
                                        <span class="badge-custom">
                                            <i class="fas fa-tag me-1"></i>
                                            <%= product.category.name %>
                                        </span>
                                    <% } %>
                                    
                                    <div class="action-buttons mt-3">
                                        <a href="/admin/products/<%= product.id %>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/admin/products/<%= product.id %>/edit" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="/admin/products/<%= product.id %>/delete" method="POST" class="d-inline">
                                            <button type="submit" class="btn btn-sm btn-danger" 
                                                    onclick="return confirm('هل أنت متأكد من حذف هذا المنتج؟')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <% }); %>
            <% } else { %>
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-box fa-5x text-muted mb-3"></i>
                        <h3 class="text-muted">لا توجد منتجات</h3>
                        <p class="text-muted">ابدأ بإضافة منتجات جديدة لمتجرك</p>
                        <a href="/admin/products/create" class="btn btn-custom">
                            <i class="fas fa-plus me-2"></i>إضافة أول منتج
                        </a>
                    </div>
                </div>
            <% } %>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
