<div class="container" dir="rtl">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>التصنيفات</h1>
        <a href="/admin/categories/create" class="btn btn-primary">إضافة تصنيف جديد</a>
    </div>

    <div class="table-responsive">
        <table class="table table-striped text-end">
            <thead>
                <tr>
                    <th>الرقم</th>
                    <th>الصورة</th>
                    <th>الاسم</th>
                    <th>الوصف</th>
                    <th>عدد المتاجر</th>
                    <th>الحالة</th>
                    <th>تاريخ الإنشاء</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <% categories.forEach(category => { %>
                    <tr>
                        <td><%= category.id %></td>
                        <td>
                            <% if (category.image) { %>
                                <img src="<%= category.image %>" alt="Category Image" width="60" height="60" style="object-fit: cover;">
                            <% } else { %>
                                <span class="text-muted">لا يوجد</span>
                            <% } %>
                        </td> 
                        <td><%= category.name %></td>
                        <td><%= category.description %></td>
                        <td><%= category.stores ? category.stores.length : 0 %></td>
                        <td>
                            <span class="badge bg-<%= category.status === 'active' ? 'success' : 'secondary' %>">
                                <%= category.status === 'active' ? 'نشط' : 'غير نشط' %>
                            </span>
                        </td>
                        <td><%= new Date(category.createdAt).toLocaleString('ar-EG') %></td>
                        <td>
                            <a href="/admin/categories/<%= category.id %>/edit" class="btn btn-sm btn-warning">تعديل</a>
                            <form action="/admin/categories/<%= category.id %>/delete" method="POST" class="d-inline">
                                <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('هل أنت متأكد من الحذف؟')">حذف</button>
                            </form>
                        </td>
                    </tr>
                <% }); %>
            </tbody>
        </table>
    </div>

    <% if (pagination.totalPages > 1) { %>
        <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
            <ul class="pagination">
                <% if (currentPage > 1) { %>
                    <li class="page-item">
                        <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
                    </li>
                <% } else { %>
                    <li class="page-item disabled">
                        <span class="page-link">السابق</span>
                    </li>
                <% } %>

            
                <% for(let i = 1; i <= pagination.totalPages; i++) { %>
                    <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                        <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                    </li>
                <% } %>

                <% if (currentPage < pagination.totalPages) { %>
                    <li class="page-item">
                        <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
                    </li>
                <% } else { %>
                    <li class="page-item disabled">
                        <span class="page-link">التالي</span>
                    </li>
                <% } %>
            </ul>
        </nav>
    <% } %>

    <% if (categories.length === 0) { %>
        <div class="alert alert-info text-center">لا توجد تصنيفات.</div>
    <% } %>
</div>
