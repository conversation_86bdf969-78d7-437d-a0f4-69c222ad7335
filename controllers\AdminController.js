const { Admin, Store, Customer, Country, Area, Category, Order, OrderDetail, Product, DeliveryPerson ,Delivery ,sequelize} = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { executeWithRetry } = require('../utils/databaseUtils');
const path = require('path');

class AdminStoreController {
  // Admin Dashboard
  async dashboard(req, res) {
    try {
      const [stores, customers, admin, countries, areas, categories] = await Promise.all([
        Store.count(),
        Customer.count(),
        Admin.findByPk(req.user.id),
        Country.count(),
        Area.count(),
        Category.count()
      ]);

      res.render('admin/dashboard', {
        stores,
        customers,
        admin,
        countries,
        areas,
        categories
      });
    } catch (error) {
      res.status(500).render('error', { error });
    }
  }

  // قائمة المتاجر مع البحث والفلتر
  async index(req, res) {
    try {
      // إعداد خيارات البحث والفلتر
      const searchFields = {
        text: ['name', 'userName', 'phoneNumber', 'address'],
        numeric: ['id']
      };

      const filterFields = {
        status: { type: 'exact' },
        areaId: { type: 'exact' },
        createdAt: { type: 'date' }
      };

      // بناء شروط البحث والفلتر
      const whereClause = buildSearchAndFilter(req.query, searchFields, filterFields);

      // خيارات الترتيب
      const sortOptions = buildSortOptions(req.query.sortBy, req.query.sortOrder, 'createdAt');

      // خيارات الـ pagination
      const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit);

      // جلب البيانات
      const { count, rows: stores } = await Store.findAndCountAll({
        where: whereClause,
        include: [
          { model: Category, as: 'categories' },
          { model: Area, as: 'area' },
          { model: Order, as: 'orders' },
          { model: DeliveryPerson, as: 'deliveryPeople' }
        ],
        order: sortOptions,
        limit: paginationOptions.limit,
        offset: paginationOptions.offset,
        distinct: true
      });

      // حساب معلومات الـ pagination
      const pagination = calculatePaginationInfo(count, paginationOptions.page, paginationOptions.limit);

      // تنظيف الفلاتر للعرض
      const filters = sanitizeFilters(req.query);

      // جلب البيانات المساعدة للفلاتر
      const [areas, categories] = await Promise.all([
        Area.findAll({ order: [['name', 'ASC']] }),
        Category.findAll({ order: [['name', 'ASC']] })
      ]);

      // خيارات الترتيب للعرض
      const sortOptionsForView = [
        { value: 'createdAt', label: 'تاريخ الإنشاء' },
        { value: 'name', label: 'اسم المتجر' },
        { value: 'userName', label: 'اسم المستخدم' },
        { value: 'status', label: 'الحالة' }
      ];

      res.render('admin/stores/index', {
        stores,
        pagination,
        filters,
        areas,
        categories,
        sortOptions: sortOptionsForView,
        currentUrl: req.originalUrl,
        originalUrl: req.originalUrl,
        activeFiltersCount: Object.keys(filters).length
      });
    } catch (error) {
      console.error('Error fetching stores:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch stores' } });
    }
  }

  // عرض نموذج إنشاء متجر جديد
  async create(req, res) {
    try {
      const [areas, categories] = await Promise.all([
        Area.findAll(),
        Category.findAll()
      ]);
      res.render('admin/stores/create', { areas, categories });
    } catch (error) {
      console.error('Error fetching data for create store:', error);
      res.status(500).render('error', { error });
    }
  }

  // حفظ متجر جديد
async store(req, res) {
  try {
    const { categoryIds, latitude, longitude, areaName, ...storeData } = req.body;
    const categories = await Category.findAll();
    // إضافة الإحداثيات إذا كانت موجودة
    if (!areaName) {
      console.log('Area name is required');
       return res.status(400).render('admin/stores/create', {
        error: 'Area name is required',
        categories // هذا المطلوب
      });
    }

    const area = await Area.findOne({ where: { name: areaName } });
    if (!area) {
      console.log(`Area "${areaName}" not found`);
       return res.status(400).render('admin/stores/create', {
        error: `Area "${areaName}" not found`,
        categories // هذا المطلوب
      });
    }

    // إذا المنطقة موجودة، اضفها للبيانات
    storeData.areaId = area.id;
      
    if (!req.files || !req.files.image) {
     return res.status(400).render('admin/stores/create', {
        error: 'Please upload an image.',
        categories // هذا المطلوب
      });
    }
    const image = req.files.image;

    // تحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(image.mimetype)) {
      console.log('Invalid image format');
      return res.status(400).render('admin/stores/create', {
        error: 'Invalid image format.',
        categories // هذا المطلوب
      });
    }

    const fileName = `${Date.now()}-${image.name}`;
    const uploadPath = path.join(__dirname, '../public/uploads', fileName);

    // حفظ الصورة على السيرفر
    await image.mv(uploadPath);

    // أضف اسم الصورة إلى بيانات المتجر (مثلاً في الحقل image)
    storeData.image = fileName;

    // إنشاء المتجر مع بيانات الصورة
    const store = await Store.create(storeData);

    if (categoryIds) {
      const ids = Array.isArray(categoryIds) ? categoryIds : [categoryIds];
      await store.setCategories(ids);
    }

    req.flash('success', 'Store created successfully with location data');
    res.redirect('/admin/stores');
  } catch (error) {
    const categories = await Category.findAll();
    console.error('Error creating store:', error);
     return res.status(400).render('admin/stores/create', {
        error: 'Error creating store '+ error ,
        categories // هذا المطلوب
      });
  }
}
  // عرض نموذج تعديل متجر
  async edit(req, res) {
    try {
      const [store, areas, categories] = await Promise.all([
        Store.findByPk(req.params.id, { include: ['categories', 'area'] }),
        Area.findAll(),
        Category.findAll()
      ]);
      if (!store) {
        return res.status(404).render('error', { error: { status: 404, message: 'Store not found' } });
      }
      res.render('admin/stores/edit', { store, areas, categories });
    } catch (error) {
      console.error('Error loading edit store:', error);
      res.status(500).render('error', { error: { message: 'Unable to load store data' } });
    }
  }

  // تحديث بيانات متجر
  async update(req, res) {
    try {
      const { categoryIds, latitude, longitude, ...storeData } = req.body;

      // إضافة الإحداثيات إذا كانت موجودة
      if (latitude && longitude) {
        storeData.latitude = parseFloat(latitude);
        storeData.longitude = parseFloat(longitude);
      }

      const store = await Store.findByPk(req.params.id);
      if (!store) {
        return res.status(404).render('error', { error: { status: 404, message: 'Store not found' } });
      }

      await store.update(storeData);

      if (categoryIds) {
        const ids = Array.isArray(categoryIds) ? categoryIds : [categoryIds];
        await store.setCategories(ids);
      }

      req.flash('success', 'Store updated successfully with location data');
      res.redirect('/admin/stores');
    } catch (error) {
      console.error('Error updating store:', error);
      res.status(500).render('error', { error: { message: 'Unable to update store' } });
    }
  }

  // حذف متجر
  async delete(req, res) {
    try {
      const store = await Store.findByPk(req.params.id);
      if (!store) {
        return res.status(404).render('error', { error: { status: 404, message: 'Store not found' } });
      }
      await store.destroy();
      req.flash('success', 'Store deleted successfully');
      res.redirect('/admin/stores');
    } catch (error) {
      console.error('Error deleting store:', error);
      res.status(500).render('error', { error: { message: 'Unable to delete store' } });
    }
  }


  // تحديث حالة متجر
  async updateStatus(req, res) {
    try {
      const { id } = req.params;
      const { status } = req.body;
      if (!['active', 'pending', 'inactive', 'banned'].includes(status)) {
        req.flash('error', 'Invalid status value');
        return res.redirect('/admin/stores/pending');
      }
      const store = await Store.findByPk(id);
      if (!store) {
        return res.status(404).render('error', { error: { status: 404, message: 'Store not found' } });
      }
      await store.update({ status });
      req.flash('success', `Store status updated to ${status}`);
      res.redirect('/admin/stores/pending');
    } catch (error) {
      console.error('Error updating store status:', error);
      res.status(500).render('error', { error: { message: 'Unable to update store status' } });
    }
  }



 // عرض الطلبات لمتجر معين مع pagination
async orders(req, res) {
  const storeId = req.params.id;
  const page = parseInt(req.query.page) || 1;
  const limit = 20;
  const offset = (page - 1) * limit;

  try {
    const store = await Store.findByPk(storeId);
    if (!store) {
      return res.status(404).render('error', { error: { message: 'Store not found' } });
    }

    // جلب الطلبات المرتبطة مع العملاء باستخدام limit و offset
    const { count, rows: orders } = await Order.findAndCountAll({
      where: { storeId },
      include: ['customer'],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/orders', {
      store,
      orders,
      currentPage: page,
      totalPages
    });

  } catch (error) {
    console.error('Error fetching store orders:', error);
    res.status(500).render('error', { error: { message: 'Unable to fetch store orders' } });
  }
}



    async show(req, res) {
        try {
            // استخدام executeWithRetry لضمان استقرار العمليات على قاعدة البيانات
            const orders = await executeWithRetry(async (transaction) => {
                // التحقق من وجود جدول الطلبات
                try {
                    await sequelize.query('SELECT TOP 1 * FROM Orders', {
                        type: sequelize.QueryTypes.SELECT,
                        transaction
                    });
                } catch (tableError) {
                    console.error('Error checking Orders table:', tableError);
                    throw new Error('Orders table may not exist or is not accessible');
                }

                // جلب الطلبات مع العلاقات اللازمة داخل المعاملة
                return await Order.findAll({
                    include: [
                        {
                            model: Customer,
                            as: 'customer',
                            required: false,
                            attributes: ['id', 'name', 'phoneNumber']
                        },
                        {
                            model: Store,
                            as: 'store',
                            required: false,
                            attributes: ['id', 'name']
                        },
                        {
                            model: OrderDetail,
                            as: 'orderDetails',
                            required: false,
                            attributes: ['id', 'quantity', 'totalPrice'],
                            include: [{
                                model: Product,
                                as: 'product',
                                required: false,
                                attributes: ['id', 'name']
                            }]
                        }
                    ],
                    attributes: ['id', 'totalPrice', 'status', 'createdAt'],
                    limit: 100,
                    order: [['createdAt', 'DESC']],
                    transaction
                });
            });

            res.render('admin/orders/show', { orders });
        } catch (error) {
            console.error('Error in OrdersController.index:', {
                message: error.message,
                stack: error.stack,
                sql: error.sql,
                parameters: error.parameters,
                retriesAttempted: error.retriesAttempted
            });

            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while fetching orders',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }
  // عرض تفاصيل الطلب
  async showDetail(req, res) {
    try {
      const order = await Order.findByPk(req.params.id, {
        include: [
          { model: Customer, as: 'customer' },
          { model: Store, as: 'store' },
          { model: OrderDetail, as: 'orderDetails', include: [{ model: Product, as: 'product' }] }
        ]
      });
      if (!order) {
        return res.status(404).render('error', { error: { message: 'Order not found' } });
      }
      res.render('admin/orders/showDetail', { order });
    } catch (error) {
      console.error('Error fetching order details:', error);
      res.status(500).render('error', { error: { message: 'Unable to fetch order details' } });
    }
  }

// عرض السائقين المرتبطين بمتجر معين مع pagination
async storeDrivers(req, res) {
  const { id: storeId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = 20;
  const offset = (page - 1) * limit;

  try {
    // التأكد من وجود المتجر فقط (بدون تحميل السائقين من هنا)
    const store = await Store.findByPk(storeId);
    if (!store) {
      return res.status(404).render('error', { error: { message: 'Store not found' } });
    }

    // جلب السائقين المرتبطين بالمتجر باستخدام limit/offset
    const { count, rows: deliveryPeoples } = await DeliveryPerson.findAndCountAll({
      where: { storeId },
      include: [{ model: Delivery, as: 'deliveries' }],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/stores/drivers', {
      store,
      deliveryPeoples,
      currentPage: page,
      totalPages
    });

  } catch (error) {
    console.error('Error fetching drivers:', error);
    res.status(500).render('error', { error: { message: 'Unable to fetch drivers' } });
  }
}


async personDeliveriesForStore(req, res) {
  const { storeId, personId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = 20;
  const offset = (page - 1) * limit;

  try {
    const { count, rows: deliveries } = await Delivery.findAndCountAll({
      where: { deliveryPersonId: personId },
      include: [
        {
          model: Order,
          as: 'order',
          where: { storeId },
          include: ['customer']
        },
        {
          model: DeliveryPerson,
          as: 'courier'
        }
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });

    const totalPages = Math.ceil(count / limit);
    const driverName = deliveries[0]?.courier?.name || 'Driver';

    res.render('admin/stores/driver-deliveries', {
      storeId,
      personId,
      driverName,
      deliveries,
      currentPage: page,
      totalPages
    });

  } catch (error) {
    console.error('Error fetching deliveries:', error);
    res.status(500).render('error', { error: { message: 'Unable to fetch deliveries for driver' } });
  }
}



}

module.exports = new AdminStoreController();
