const { Admin, Customer, Category, Order, OrderDetail, Product, DeliveryPerson, Delivery, sequelize} = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { executeWithRetry } = require('../utils/databaseUtils');
const path = require('path');

class AdminStoreController {
  // Admin Dashboard
  async dashboard(req, res) {
    try {
      const [customers, admin, categories, orders, products, deliveryPeople] = await Promise.all([
        Customer.count(),
        Admin.findByPk(req.user.id),
        Category.count(),
        Order.count(),
        Product.count(),
        DeliveryPerson.count()
      ]);

      res.render('admin/dashboard', {
        customers,
        admin,
        categories,
        orders,
        products,
        deliveryPeople
      });
    } catch (error) {
      res.status(500).render('error', { error });
    }
  }
}

module.exports = new AdminStoreController();
