<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>إدارة الطلبات</h1>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>إجمالي السعر</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (orders && orders.length > 0) { %>
                            <% orders.forEach(order => { %>
                                <tr>
                                    <td>#<%= order.id %></td>
                                    <td>
                                        <% if (order.customer) { %>
                                            <%= order.customer.name %>
                                            <br><small class="text-muted"><%= order.customer.phoneNumber %></small>
                                        <% } else { %>
                                            <span class="text-muted">لا يوجد عميل</span>
                                        <% } %>
                                    </td>
                                    <td><%= order.totalPrice %> ر.س</td>
                                    <td>
                                        <%
                                            let badgeClass = 'secondary';
                                            let statusText = order.status;

                                            if (order.status === 'pending') {
                                                badgeClass = 'warning';
                                                statusText = 'في الانتظار';
                                            } else if (order.status === 'approved') {
                                                badgeClass = 'info';
                                                statusText = 'موافق عليه';
                                            } else if (order.status === 'rejected') {
                                                badgeClass = 'danger';
                                                statusText = 'مرفوض';
                                            } else if (order.status === 'delivered') {
                                                badgeClass = 'success';
                                                statusText = 'تم التسليم';
                                            }
                                        %>
                                        <span class="badge bg-<%= badgeClass %>">
                                            <%= statusText %>
                                        </span>
                                    </td>
                                    <td><%= new Date(order.createdAt).toLocaleDateString('ar-SA') %></td>
                                    <td>
                                        <a href="/admin/orders/<%= order.id %>" class="btn btn-sm btn-info">عرض</a>

                                        <% if (order.status === 'pending') { %>
                                            <button class="btn btn-sm btn-success" onclick="approveOrder(<%= order.id %>)">
                                                موافقة
                                            </button>
                                            <button class="btn btn-sm btn-danger" onclick="rejectOrder(<%= order.id %>)">
                                                رفض
                                            </button>
                                        <% } %>

                                        <% if (order.status === 'approved') { %>
                                            <button class="btn btn-sm btn-warning" onclick="assignDelivery(<%= order.id %>)">
                                                تسليم
                                            </button>
                                        <% } %>
                                    </td>
                                </tr>
                            <% }); %>
                        <% } else { %>
                            <tr>
                                <td colspan="6" class="text-center">لا توجد طلبات</td>
                            </tr>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

        <% if (totalPages > 1) { %>
            <nav aria-label="Page navigation" class="d-flex justify-content-center mt-4">
              <ul class="pagination">
                <% if (currentPage > 1) { %>
                  <li class="page-item">
                    <a class="page-link" href="?page=<%= currentPage - 1 %>">السابق</a>
                  </li>
                <% } else { %>
                  <li class="page-item disabled">
                    <span class="page-link">السابق</span>
                  </li>
                <% } %>
            
                <% for(let i = 1; i <= totalPages; i++) { %>
                  <li class="page-item <%= currentPage === i ? 'active' : '' %>">
                    <a class="page-link" href="?page=<%= i %>"><%= i %></a>
                  </li>
                <% } %>
            
                <% if (currentPage < totalPages) { %>
                  <li class="page-item">
                    <a class="page-link" href="?page=<%= currentPage + 1 %>">التالي</a>
                  </li>
                <% } else { %>
                  <li class="page-item disabled">
                    <span class="page-link">التالي</span>
                  </li>
                <% } %>
              </ul>
            </nav>
        <% } %>
    <% } %>
</div>

<script>
// وظائف إدارة الطلبات
function approveOrder(orderId) {
    if (confirm('هل أنت متأكد من الموافقة على هذا الطلب؟')) {
        fetch(`/admin/orders/${orderId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم الموافقة على الطلب بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء الموافقة على الطلب');
        });
    }
}

function rejectOrder(orderId) {
    const reason = prompt('أدخل سبب الرفض:');
    if (reason) {
        fetch(`/admin/orders/${orderId}/reject`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ reason })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم رفض الطلب');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء رفض الطلب');
        });
    }
}

function assignDelivery(orderId) {
    // هنا يمكن إضافة نافذة لاختيار مندوب التوصيل
    alert('سيتم إضافة وظيفة تسليم الطلب لمندوب التوصيل قريباً');
}
</script>
</div>
