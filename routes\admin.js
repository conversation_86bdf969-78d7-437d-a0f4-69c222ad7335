const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const AdminController = require('../controllers/AdminController');
const CategoryController = require('../controllers/CategoryController');
const CustomersController = require('../controllers/CustomersController');
const ProductsController = require('../controllers/ProductsController');
const OrdersController = require('../controllers/OrdersController');
const { requireAdminAuth } = require('./admin-auth');

// إعداد multer لرفع الصور
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, 'public/uploads/customers/');
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'customer-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
        fieldSize: 10 * 1024 * 1024, // 10MB for form fields
        fields: 20, // Maximum number of non-file fields
        files: 1 // Maximum number of file fields
    },
    fileFilter: function (req, file, cb) {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('يجب أن يكون الملف صورة'), false);
        }
    }
});

// Apply authentication middleware to all admin routes
router.use(requireAdminAuth);

// Dashboard route
router.get('/', (req, res) => {
  res.redirect('/admin/dashboard');
});

router.get('/dashboard', AdminController.dashboard.bind(AdminController));

// Categories routes
router.get('/categories', CategoryController.index.bind(CategoryController));
router.get('/categories/create', CategoryController.create.bind(CategoryController));
router.post('/categories', CategoryController.store.bind(CategoryController));
router.get('/categories/:id', CategoryController.show.bind(CategoryController));
router.get('/categories/:id/edit', CategoryController.edit.bind(CategoryController));
router.post('/categories/:id', CategoryController.update.bind(CategoryController));
router.post('/categories/:id/delete', CategoryController.delete.bind(CategoryController));

// Products routes
router.get('/products', ProductsController.index.bind(ProductsController));
router.get('/products/create', ProductsController.create.bind(ProductsController));
router.post('/products', ProductsController.store.bind(ProductsController));
router.get('/products/:id', ProductsController.show.bind(ProductsController));
router.get('/products/:id/edit', ProductsController.edit.bind(ProductsController));
router.post('/products/:id', ProductsController.update.bind(ProductsController));
router.post('/products/:id/delete', ProductsController.destroy.bind(ProductsController));

// Customers routes
router.get('/customers', CustomersController.index.bind(CustomersController));
router.get('/customers/create', CustomersController.create.bind(CustomersController));
router.post('/customers', (req, res, next) => {
    upload.single('image')(req, res, (err) => {
        if (err) {
            console.log('Upload error:', err);
            // إذا كان الخطأ متعلق بعدم وجود ملف، تجاهله
            if (err.code === 'LIMIT_UNEXPECTED_FILE' || err.message.includes('Unexpected end of form')) {
                return next();
            }
            // إذا كان خطأ آخر، أرسل رسالة خطأ
            return res.status(400).json({
                success: false,
                message: err.message || 'خطأ في رفع الملف'
            });
        }
        next();
    });
}, CustomersController.store.bind(CustomersController));
router.get('/customers/:id', CustomersController.show.bind(CustomersController));
router.get('/customers/:id/edit', CustomersController.edit.bind(CustomersController));
router.post('/customers/:id', (req, res, next) => {
    upload.single('image')(req, res, (err) => {
        if (err) {
            console.log('Upload error:', err);
            // إذا كان الخطأ متعلق بعدم وجود ملف، تجاهله
            if (err.code === 'LIMIT_UNEXPECTED_FILE' || err.message.includes('Unexpected end of form')) {
                return next();
            }
            // إذا كان خطأ آخر، أرسل رسالة خطأ
            return res.status(400).json({
                success: false,
                message: err.message || 'خطأ في رفع الملف'
            });
        }
        next();
    });
}, CustomersController.update.bind(CustomersController));
router.post('/customers/:id/delete', CustomersController.delete.bind(CustomersController));
router.post('/customers/:id/status', CustomersController.updateStatus.bind(CustomersController));

// Orders routes
router.get('/orders', OrdersController.index.bind(OrdersController));
router.get('/orders/:id', OrdersController.show.bind(OrdersController));
router.post('/orders/:id/approve', OrdersController.approve.bind(OrdersController));
router.post('/orders/:id/reject', OrdersController.reject.bind(OrdersController));
router.post('/orders/:id/assign-delivery', OrdersController.assignDelivery.bind(OrdersController));
router.post('/orders/:id/status', OrdersController.updateStatus.bind(OrdersController));

module.exports = router;
