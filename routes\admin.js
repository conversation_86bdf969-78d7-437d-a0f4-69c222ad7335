const express = require('express');
const router = express.Router();
const AdminController = require('../controllers/AdminController');
const CategoryController = require('../controllers/CategoryController');
const CustomersController = require('../controllers/CustomersController');
const ProductsController = require('../controllers/ProductsController');
const OrdersController = require('../controllers/OrdersController');
const { requireAdminAuth } = require('./admin-auth');

// إعداد multer لرفع الصور
const { uploadProducts, uploadCategories, handleMulterError } = require('../middleware/upload');

// Apply authentication middleware to all admin routes
router.use(requireAdminAuth);

// Dashboard route
router.get('/', (req, res) => {
  res.redirect('/admin/dashboard');
});

router.get('/dashboard', AdminController.dashboard.bind(AdminController));

// Categories routes
router.get('/categories', CategoryController.index.bind(CategoryController));
router.get('/categories/create', CategoryController.create.bind(CategoryController));
router.post('/categories',uploadCategories.single('image'), CategoryController.store.bind(CategoryController));
router.get('/categories/:id', CategoryController.show.bind(CategoryController));
router.get('/categories/:id/edit', CategoryController.edit.bind(CategoryController));
router.post('/categories/:id', CategoryController.update.bind(CategoryController));
router.post('/categories/:id/delete', CategoryController.delete.bind(CategoryController));

// Products routes
router.get('/products', ProductsController.index.bind(ProductsController));
router.get('/products/create', ProductsController.create.bind(ProductsController));
router.post('/products', uploadProducts.array('images', 10), ProductsController.store.bind(ProductsController));
router.get('/products/:id', ProductsController.show.bind(ProductsController));
router.get('/products/:id/edit', ProductsController.edit.bind(ProductsController));
router.post('/products/:id', uploadProducts.array('images', 10), ProductsController.update.bind(ProductsController));
router.post('/products/:id/delete', ProductsController.destroy.bind(ProductsController));
router.post('/products/images/:id/delete', ProductsController.deleteImage.bind(ProductsController));

// Customers routes
router.get('/customers', CustomersController.index.bind(CustomersController));
router.get('/customers/create', CustomersController.create.bind(CustomersController));
router.post('/customers', CustomersController.store.bind(CustomersController));
router.get('/customers/:id', CustomersController.show.bind(CustomersController));
router.get('/customers/:id/edit', CustomersController.edit.bind(CustomersController));
router.post('/customers/:id', CustomersController.update.bind(CustomersController));
router.post('/customers/:id/delete', CustomersController.delete.bind(CustomersController));
router.post('/customers/:id/status', CustomersController.updateStatus.bind(CustomersController));

// Orders routes
router.get('/orders', OrdersController.index.bind(OrdersController));
router.get('/orders/:id', OrdersController.show.bind(OrdersController));
router.post('/orders/:id/approve', OrdersController.approve.bind(OrdersController));
router.post('/orders/:id/reject', OrdersController.reject.bind(OrdersController));
router.post('/orders/:id/assign-delivery', OrdersController.assignDelivery.bind(OrdersController));
router.post('/orders/:id/status', OrdersController.updateStatus.bind(OrdersController));

module.exports = router;
