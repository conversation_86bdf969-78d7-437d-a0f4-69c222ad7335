<div class="container my-5">
  <div class="card shadow-sm p-4">
    <h2 class="mb-4 text-primary">تفاصيل الطلب #<%= order.id %></h2>

    <div class="row mb-3">
      <div class="col-md-6">
        <strong>الزبون:</strong> <%= order.customer ? order.customer.name : 'غير متوفر' %><br>
        <strong>المتجر:</strong> <%= order.store ? order.store.name : 'غير متوفر' %>
      </div>
      <div class="col-md-6 text-end">
        <span class="badge bg-<%= order.status === 'pending' ? 'warning' : order.status === 'completed' ? 'success' : order.status === 'cancelled' ? 'danger' : 'secondary' %>">
          <%= order.status === 'pending' ? 'قيد المعالجة' :
               order.status === 'completed' ? 'مكتمل' :
               order.status === 'cancelled' ? 'ملغي' : 'غير معروف' %>
        </span><br>
        <small class="text-muted">تاريخ الإنشاء: <%= new Date(order.createdAt).toLocaleString('ar-EG') %></small>
      </div>
    </div>

    <hr>

    <h4 class="mt-4 mb-3">المنتجات المطلوبة</h4>
    <div class="table-responsive">
      <table class="table table-bordered text-center align-middle">
        <thead class="table-light">
          <tr>
            <th>#</th>
            <th>المنتج</th>
            <th>الكمية</th>
            <th>السعر الكلي</th>
            <th>ملاحظات</th>
          </tr>
        </thead>
        <tbody>
          <% order.orderDetails.forEach((detail, index) => { %>
            <tr>
              <td><%= index + 1 %></td>
              <td><%= detail.product ? detail.product.name : 'غير متوفر' %></td>
              <td><%= detail.quantity %></td>
              <td>$<%= detail.totalPrice?.toFixed(2) || '0.00' %></td>
              <td><%= detail.notes || '-' %></td>
            </tr>
          <% }) %>
        </tbody>
      </table>
    </div>

    <div class="mt-4">
      <a href="/admin/orders" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> العودة للطلبات
      </a>
    </div>
  </div>
</div>
