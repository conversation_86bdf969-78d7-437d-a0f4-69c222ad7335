<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>إدارة العملاء</h1>
        <a href="/admin/customers/create" class="btn btn-primary">
            <i class="fas fa-plus"></i> إضافة عميل جديد
        </a>
    </div>

    <!-- بحث بسيط -->
    <div class="row mb-4">
        <div class="col-md-6">
            <form method="GET" action="/admin/customers">
                <div class="input-group">
                    <input type="text" class="form-control" name="search"
                           placeholder="البحث في العملاء..."
                           value="<%= typeof filters !== 'undefined' && filters.search ? filters.search : '' %>">
                    <button class="btn btn-primary" type="submit">
                        <i class="fas fa-search"></i> بحث
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="thead-dark">
                <tr>
                    <th>
                        <a href="?sortBy=id&sortOrder=<%= (filters.sortBy === 'id' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            ID
                            <% if (filters.sortBy === 'id') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>
                        <a href="?sortBy=name&sortOrder=<%= (filters.sortBy === 'name' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            اسم العميل
                            <% if (filters.sortBy === 'name') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>رقم الهاتف</th>
                    <th>الباركود</th>
                    <th>البريد الإلكتروني</th>
                    <th>نسبة الخصم</th>
                    <th>الطلبات</th>
                    <th>
                        <a href="?sortBy=status&sortOrder=<%= (filters.sortBy === 'status' && filters.sortOrder === 'asc') ? 'desc' : 'asc' %>"
                           class="text-white text-decoration-none">
                            الحالة
                            <% if (filters.sortBy === 'status') { %>
                                <i class="fas fa-sort-<%= filters.sortOrder === 'asc' ? 'up' : 'down' %>"></i>
                            <% } else { %>
                                <i class="fas fa-sort text-muted"></i>
                            <% } %>
                        </a>
                    </th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <% customers.forEach(customer => { %>
                    <tr>
                        <td><%= customer.id %></td>
                        <td><%= customer.name %></td>
                        <td><%= customer.phoneNumber || 'غير محدد' %></td>
                        <td>
                            <% if (customer.barcode) { %>
                                <code><%= customer.barcode %></code>
                            <% } else { %>
                                <span class="text-muted">غير محدد</span>
                            <% } %>
                        </td>
                        <td>
                            <%= customer.email || 'غير محدد' %>
                        </td>
                        <td>
                            <span class="text-muted">0%</span>
                        </td>
                        <td>
                            <span class="badge badge-primary"><%= customer.orders ? customer.orders.length : 0 %></span>
                        </td>
                        <td>
                            <% if (customer.status === 'active') { %>
                                <span class="badge badge-success">نشط</span>
                            <% } else if (customer.status === 'inactive') { %>
                                <span class="badge badge-danger">غير نشط</span>
                            <% } else if (customer.status === 'pending') { %>
                                <span class="badge badge-warning">في الانتظار</span>
                            <% } else { %>
                                <span class="badge badge-secondary">غير محدد</span>
                            <% } %>
                        </td>
                        <td class="action-buttons">
                            <div class="btn-group" role="group">
                                <% if (customer.status !== 'active') { %>
                                    <form action="/admin/customers/<%= customer.id %>/status" method="POST" class="d-inline">
                                        <input type="hidden" name="status" value="active">
                                        <button type="submit" class="btn btn-sm btn-success" title="تفعيل">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                <% } %>
                                <a href="/admin/customers/<%= customer.id %>/edit"
                                   class="btn btn-sm btn-warning"
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="/admin/customers/<%= customer.id %>"
                                   class="btn btn-sm btn-info"
                                   title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <form action="/admin/customers/<%= customer.id %>/delete" method="POST" class="d-inline">
                                    <button type="submit"
                                            class="btn btn-sm btn-danger"
                                            title="حذف"
                                            onclick="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                <% }); %>

                <% if (!customers || customers.length === 0) { %>
                    <tr>
                        <td colspan="8" class="text-center py-4">
                            <div class="text-muted">
                                <i class="fas fa-users fa-3x mb-3"></i>
                                <p class="mb-0">لا توجد عملاء مطابقين للبحث</p>
                                <% if (Object.keys(filters || {}).length > 0) { %>
                                    <a href="/admin/customers" class="btn btn-sm btn-primary mt-2">
                                        <i class="fas fa-times"></i> مسح الفلاتر
                                    </a>
                                <% } %>
                            </div>
                        </td>
                    </tr>
                <% } %>
            </tbody>
        </table>
    </div>

    <!-- الـ Pagination الجديد -->
    <%- include('../../partials/pagination', {
        pagination: pagination || null,
        currentUrl: currentUrl || '',
        originalUrl: originalUrl || ''
    }) %>
</div>