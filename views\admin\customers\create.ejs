<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1>إضافة عميل جديد</h1>
        <a href="/admin/customers" class="btn btn-secondary">العودة للعملاء</a>
    </div>

    <% if (typeof error !== 'undefined' && error) { %>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <%= error %>
        </div>
    <% } %>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <form action="/admin/customers" method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">اسم العميل *</label>
                                    <input type="text" class="form-control" id="name" name="name"
                                           value="<%= typeof formData !== 'undefined' ? formData.name || '' : '' %>"
                                           required placeholder="أدخل اسم العميل">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phoneNumber" class="form-label">رقم الهاتف *</label>
                                    <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber"
                                           value="<%= typeof formData !== 'undefined' ? formData.phoneNumber || '' : '' %>"
                                           required placeholder="أدخل رقم الهاتف (مثال: 0501234567)">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" id="password" name="password"
                                           required placeholder="أدخل كلمة المرور">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="barcode" class="form-label">الباركود</label>
                                    <input type="text" class="form-control" id="barcode" name="barcode"
                                           value="<%= typeof formData !== 'undefined' ? formData.barcode || '' : '' %>"
                                           placeholder="أدخل الباركود (اختياري)">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discountRate" class="form-label">نسبة الخصم (%)</label>
                                    <input type="number" class="form-control" id="discountRate" name="discountRate"
                                           value="<%= typeof formData !== 'undefined' ? formData.discountRate || '0' : '0' %>"
                                           min="0" max="100" step="0.01" placeholder="أدخل نسبة الخصم">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">الحالة</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active" <%= typeof formData !== 'undefined' && formData.status === 'active' ? 'selected' : 'selected' %>>نشط</option>
                                        <option value="inactive" <%= typeof formData !== 'undefined' && formData.status === 'inactive' ? 'selected' : '' %>>غير نشط</option>
                                        <option value="pending" <%= typeof formData !== 'undefined' && formData.status === 'pending' ? 'selected' : '' %>>في الانتظار</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="image" class="form-label">صورة العميل</label>
                                    <input type="file" class="form-control" id="image" name="image"
                                           accept="image/*" placeholder="اختر صورة العميل">
                                    <small class="form-text text-muted">اختياري - يمكن رفع صورة للعميل</small>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <div class="input-group">
                                        <textarea class="form-control" id="address" name="address" rows="3"
                                                  placeholder="أدخل عنوان العميل أو اختر من الخريطة"><%= typeof formData !== 'undefined' ? formData.address || '' : '' %></textarea>
                                        <button type="button" class="btn btn-outline-primary" id="selectFromMap">
                                            <i class="fas fa-map-marker-alt"></i> اختر من الخريطة
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="latitude" class="form-label">خط العرض (Latitude)</label>
                                    <input type="number" class="form-control" id="latitude" name="latitude"
                                           step="0.00000001" min="-90" max="90" readonly
                                           value="<%= typeof formData !== 'undefined' ? formData.latitude || '' : '' %>"
                                           placeholder="سيتم ملؤه تلقائياً من الخريطة">
                                    <small class="form-text text-muted">سيتم ملؤه تلقائياً عند اختيار الموقع من الخريطة</small>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="longitude" class="form-label">خط الطول (Longitude)</label>
                                    <input type="number" class="form-control" id="longitude" name="longitude"
                                           step="0.00000001" min="-180" max="180" readonly
                                           value="<%= typeof formData !== 'undefined' ? formData.longitude || '' : '' %>"
                                           placeholder="سيتم ملؤه تلقائياً من الخريطة">
                                    <small class="form-text text-muted">سيتم ملؤه تلقائياً عند اختيار الموقع من الخريطة</small>
                                </div>
                            </div>

                            <!-- خريطة لاختيار الموقع -->
                            <div class="col-12">
                                <div class="mb-3">
                                    <div id="mapContainer" style="display: none;">
                                        <div class="card">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0">اختر موقع العميل</h6>
                                                <button type="button" class="btn btn-sm btn-secondary" id="closeMap">إغلاق</button>
                                            </div>
                                            <div class="card-body">
                                                <div id="map" style="height: 400px; width: 100%;"></div>
                                                <div class="mt-3">
                                                    <button type="button" class="btn btn-success" id="confirmLocation">تأكيد الموقع</button>
                                                    <button type="button" class="btn btn-secondary" id="getCurrentLocation">موقعي الحالي</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4"
                                              placeholder="أدخل أي ملاحظات حول العميل"><%= typeof formData !== 'undefined' ? formData.notes || '' : '' %></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-3">
                            <a href="/admin/customers" class="btn btn-secondary">إلغاء</a>
                            <button type="submit" class="btn btn-primary">حفظ العميل</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إضافة Google Maps API -->
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBOti4mM-6x9WDnZIjIeyEU21OpBXqWBgw&libraries=places&language=ar"></script>

<script>
let map;
let marker;
let selectedLocation = null;

document.addEventListener('DOMContentLoaded', function() {
    // إظهار/إخفاء الخريطة
    document.getElementById('selectFromMap').addEventListener('click', function() {
        document.getElementById('mapContainer').style.display = 'block';
        initMap();
    });

    document.getElementById('closeMap').addEventListener('click', function() {
        document.getElementById('mapContainer').style.display = 'none';
    });

    // تأكيد الموقع المختار
    document.getElementById('confirmLocation').addEventListener('click', function() {
        if (selectedLocation) {
            document.getElementById('latitude').value = selectedLocation.lat;
            document.getElementById('longitude').value = selectedLocation.lng;

            // الحصول على العنوان من الإحداثيات
            getAddressFromCoordinates(selectedLocation.lat, selectedLocation.lng);

            document.getElementById('mapContainer').style.display = 'none';
        } else {
            alert('يرجى اختيار موقع على الخريطة أولاً');
        }
    });

    // الحصول على الموقع الحالي
    document.getElementById('getCurrentLocation').addEventListener('click', function() {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                selectedLocation = { lat: lat, lng: lng };

                // تحديث الخريطة
                map.setCenter(selectedLocation);
                marker.setPosition(selectedLocation);

                // تحديث الحقول
                document.getElementById('latitude').value = lat;
                document.getElementById('longitude').value = lng;

                // الحصول على العنوان
                getAddressFromCoordinates(lat, lng);

            }, function() {
                alert('لا يمكن الحصول على موقعك الحالي');
            });
        } else {
            alert('المتصفح لا يدعم خدمة تحديد الموقع');
        }
    });
});

function initMap() {
    // الموقع الافتراضي (الرياض)
    const defaultLocation = { lat: 24.7136, lng: 46.6753 };

    // إنشاء الخريطة
    map = new google.maps.Map(document.getElementById('map'), {
        zoom: 10,
        center: defaultLocation,
        mapTypeId: 'roadmap'
    });

    // إنشاء العلامة
    marker = new google.maps.Marker({
        position: defaultLocation,
        map: map,
        draggable: true,
        title: 'اسحب لتحديد الموقع'
    });

    // عند النقر على الخريطة
    map.addListener('click', function(event) {
        selectedLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
        };
        marker.setPosition(selectedLocation);
    });

    // عند سحب العلامة
    marker.addListener('dragend', function(event) {
        selectedLocation = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
        };
    });
}

function getAddressFromCoordinates(lat, lng) {
    const geocoder = new google.maps.Geocoder();
    const latlng = { lat: parseFloat(lat), lng: parseFloat(lng) };

    geocoder.geocode({ location: latlng }, function(results, status) {
        if (status === 'OK') {
            if (results[0]) {
                document.getElementById('address').value = results[0].formatted_address;
            }
        } else {
            console.log('Geocoder failed due to: ' + status);
        }
    });
}
</script>