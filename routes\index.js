const express = require('express');
const router = express.Router();

// استيراد الطرق الأساسية
const adminRouter = require('./admin');
const adminAuth = require('./admin-auth');
const customersRouter = require('./customers');
const imageRouter = require('./images');
const notificationsRouter = require('./notifications');
const customerNotificationsRouter = require('./customer-notifications');

// ربط المسارات الأساسية
router.use('/admin/auth', adminAuth.router);
router.use('/admin', adminRouter);
router.use('/customers', customersRouter);
router.use('/customers/notifications', customerNotificationsRouter);
router.use('/images', imageRouter);
router.use('/notifications', notificationsRouter);

// إضافة مسار الصفحة الرئيسية
router.get('/', (req, res) => {
    res.render('home', {
        title: 'نظام إدارة المتجر',
        user: res.locals.user
    });
});

module.exports = router;
