const BaseController = require('./BaseController');
const { Order, Customer, Product, OrderDetail, DeliveryPerson, Delivery, Notification, sequelize } = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { Op } = require('sequelize');

class OrdersController extends BaseController {
    constructor() {
        super(Order, 'orders');
    }

    // عرض قائمة الطلبات للإدارة
    async index(req, res) {
        try {
            // إعداد خيارات البحث والفلتر
            const searchFields = {
                text: ['id'],
                numeric: ['id', 'totalPrice']
            };

            const filterFields = {
                status: { type: 'exact' },
                customerId: { type: 'exact' },
                createdAt: { type: 'date' }
            };

            // بناء شروط البحث والفلتر
            const whereClause = buildSearchAndFilter(req.query, searchFields, filterFields);

            // خيارات الترتيب
            const sortOptions = buildSortOptions(req.query.sortBy, req.query.sortOrder, 'createdAt');

            // خيارات الـ pagination
            const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit);

            // جلب البيانات
            const { count, rows: orders } = await Order.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product',
                            attributes: ['id', 'name', 'price']
                        }]
                    }
                ],
                order: sortOptions,
                limit: paginationOptions.limit,
                offset: paginationOptions.offset,
                distinct: true
            });

            // حساب معلومات الـ pagination
            const pagination = calculatePaginationInfo(count, paginationOptions.page, paginationOptions.limit);

            // تنظيف الفلاتر للعرض
            const filters = sanitizeFilters(req.query);

            // جلب العملاء للفلتر
            const customers = await Customer.findAll({
                attributes: ['id', 'name'],
                order: [['name', 'ASC']]
            });

            res.render('admin/orders/index', {
                orders,
                pagination,
                filters,
                customers,
                currentUrl: req.originalUrl,
                originalUrl: req.originalUrl,
                activeFiltersCount: Object.keys(filters).length
            });
        } catch (error) {
            console.error('Error fetching orders:', error);
            res.status(500).render('error', { error: { message: 'Unable to fetch orders' } });
        }
    }

    async create(req, res) {
        try {
            const [customers, stores, products] = await Promise.all([
                executeWithRetry(async () => Customer.findAll({ where: { status: 'active' } }),
                    { useTransaction: false }),
                executeWithRetry(async () => Store.findAll({ where: { status: 'active' } }),
                    { useTransaction: false }),
                executeWithRetry(async () => Product.findAll({
                    where: {
                        status: 'active',
                        quantity: { [sequelize.Op.gt]: 0 }
                    }}),
                    { useTransaction: false })
            ]);

            res.render('orders/create', { customers, stores, products });
        } catch (error) {
            console.error('Error in OrdersController.create:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while loading the create form',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async store(req, res) {
        try {
            let createdOrder = null;

            await executeWithRetry(async (transaction) => {
                const { orderDetails, ...orderData } = req.body;
                const order = await Order.create(orderData, { transaction });
                createdOrder = order;

                if (Array.isArray(orderDetails)) {
                    // Calculate totalPrice for each order detail
                    const detailsWithTotalPrice = await Promise.all(
                        orderDetails.map(async (detail) => {
                            const product = await Product.findByPk(detail.productId, { transaction });
                            if (!product) {
                                throw new Error(`Product with ID ${detail.productId} not found`);
                            }
                            return {
                                ...detail,
                                orderId: order.id,
                                totalPrice: product.price * detail.quantity
                            };
                        })
                    );
                    await OrderDetail.bulkCreate(detailsWithTotalPrice, { transaction });
                }
            });

            // إرسال إشعار للمتجر عن الطلب الجديد
            if (createdOrder) {
                try {
                    await this.sendNewOrderNotifications(createdOrder);
                } catch (notificationError) {
                    logger.error('Error sending order notifications:', notificationError);
                    // لا نوقف العملية إذا فشل الإشعار
                }
            }

            req.flash('success', 'Order created successfully');
            res.redirect('/orders');
        } catch (error) {
            console.error('Error in OrdersController.store:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while creating the order',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    /**
     * إرسال إشعارات الطلب الجديد
     */
    async sendNewOrderNotifications(order) {
        try {
            // جلب تفاصيل الطلب مع العلاقات
            const orderWithDetails = await Order.findByPk(order.id, {
                include: [
                    {
                        model: Customer,
                        as: 'customer',
                        attributes: ['id', 'name', 'phoneNumber']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [{
                            model: Product,
                            as: 'product',
                            include: [{
                                model: Store,
                                as: 'store',
                                attributes: ['id', 'name', 'userName']
                            }]
                        }]
                    }
                ]
            });

            if (!orderWithDetails) {
                logger.error('Order not found for notifications:', order.id);
                return;
            }

            // تجميع المتاجر المشاركة في الطلب
            const storesInOrder = new Map();
            let totalAmount = 0;

            orderWithDetails.orderDetails.forEach(detail => {
                const store = detail.product.store;
                const itemTotal = detail.quantity * detail.price;
                totalAmount += itemTotal;

                if (!storesInOrder.has(store.id)) {
                    storesInOrder.set(store.id, {
                        store: store,
                        items: [],
                        totalAmount: 0
                    });
                }

                const storeData = storesInOrder.get(store.id);
                storeData.items.push({
                    product: detail.product.name,
                    quantity: detail.quantity,
                    price: detail.price,
                    total: itemTotal
                });
                storeData.totalAmount += itemTotal;
            });

            // إرسال إشعار لكل متجر
            for (const [storeId, storeData] of storesInOrder) {
                const itemsList = storeData.items.map(item =>
                    `${item.product} (${item.quantity} × ${item.price} ريال)`
                ).join('\n');

                await NotificationService.notifyStore(storeId, {
                    title: 'طلب جديد يحتاج للمعالجة',
                    message: `لديك طلب جديد رقم #${orderWithDetails.id} من العميل ${orderWithDetails.customer.name}\n\nالمنتجات:\n${itemsList}\n\nإجمالي المبلغ: ${storeData.totalAmount} ريال`,
                    type: 'order',
                    priority: 'high',
                    actionUrl: `/store/orders/${orderWithDetails.id}`,
                    actionText: 'معالجة الطلب',
                    data: {
                        orderId: orderWithDetails.id,
                        customerId: orderWithDetails.customer.id,
                        customerName: orderWithDetails.customer.name,
                        totalAmount: storeData.totalAmount,
                        itemsCount: storeData.items.length,
                        type: 'new_order'
                    }
                });
            }

            // إرسال إشعار تأكيد للعميل
            await NotificationService.notifyCustomer(orderWithDetails.customer.id, {
                title: 'تم إنشاء طلبك بنجاح',
                message: `تم إنشاء طلبك رقم #${orderWithDetails.id} بنجاح بإجمالي ${totalAmount} ريال. سيتم معالجته من قبل المتاجر قريباً.`,
                type: 'success',
                priority: 'normal',
                actionUrl: `/customers/orders/${orderWithDetails.id}`,
                actionText: 'عرض الطلب',
                data: {
                    orderId: orderWithDetails.id,
                    totalAmount: totalAmount,
                    storesCount: storesInOrder.size,
                    type: 'order_created'
                }
            });

            logger.info(`Order notifications sent for order ${orderWithDetails.id} to ${storesInOrder.size} stores and customer ${orderWithDetails.customer.id}`);

        } catch (error) {
            logger.error('Error in sendNewOrderNotifications:', error);
            throw error;
        }
    }

    async show(req, res) {
        try {
            const order = await executeWithRetry(async (transaction) => {
                return await Order.findByPk(req.params.id, {
                    include: [
                        {
                            model: Customer,
                            as: 'customer',
                            attributes: ['id', 'name', 'phoneNumber']
                        },
                        {
                            model: Store,
                            as: 'store',
                            attributes: ['id', 'name']
                        },
                        {
                            model: OrderDetail,
                            as: 'orderDetails',
                            include: [{
                                model: Product,
                                as: 'product',
                                attributes: ['id', 'name', 'price']
                            }]
                        }
                    ],
                    transaction
                });
            });

            if (!order) {
                return res.status(404).render('error', {
                    error: { message: 'Order not found' }
                });
            }

            res.render('admin/orders/show', {
                order,
                deliveryPeople: [],
                title: `تفاصيل الطلب #${order.id}`
            });
        } catch (error) {
            console.error('Error in OrdersController.show:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while fetching the order',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    // الموافقة على طلب
    async approve(req, res) {
        try {
            const order = await Order.findByPk(req.params.id);

            if (!order) {
                return res.status(404).json({ success: false, message: 'الطلب غير موجود' });
            }

            if (order.status !== 'pending') {
                return res.status(400).json({ success: false, message: 'لا يمكن الموافقة على هذا الطلب' });
            }

            // تحديث حالة الطلب
            await order.update({ status: 'approved' });

            // إرسال إشعار للعميل
            await Notification.create({
                customerId: order.customerId,
                title: 'تم الموافقة على طلبك',
                message: `تم الموافقة على طلبك رقم #${order.id} وسيتم تحضيره قريباً`,
                type: 'order_approved',
                relatedId: order.id
            });

            res.json({ success: true, message: 'تم الموافقة على الطلب بنجاح' });
        } catch (error) {
            console.error('Error approving order:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء الموافقة على الطلب' });
        }
    }

    // رفض طلب
    async reject(req, res) {
        try {
            const order = await Order.findByPk(req.params.id);

            if (!order) {
                return res.status(404).json({ success: false, message: 'الطلب غير موجود' });
            }

            if (order.status !== 'pending') {
                return res.status(400).json({ success: false, message: 'لا يمكن رفض هذا الطلب' });
            }

            const { reason } = req.body;

            // تحديث حالة الطلب
            await order.update({
                status: 'rejected',
                rejectionReason: reason || 'لم يتم تحديد السبب'
            });

            // إرسال إشعار للعميل
            await Notification.create({
                customerId: order.customerId,
                title: 'تم رفض طلبك',
                message: `تم رفض طلبك رقم #${order.id}. السبب: ${reason || 'لم يتم تحديد السبب'}`,
                type: 'order_rejected',
                relatedId: order.id
            });

            res.json({ success: true, message: 'تم رفض الطلب' });
        } catch (error) {
            console.error('Error rejecting order:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء رفض الطلب' });
        }
    }

    // تسليم طلب لمندوب توصيل
    async assignDelivery(req, res) {
        try {
            const order = await Order.findByPk(req.params.id);

            if (!order) {
                return res.status(404).json({ success: false, message: 'الطلب غير موجود' });
            }

            if (order.status !== 'approved') {
                return res.status(400).json({ success: false, message: 'يجب الموافقة على الطلب أولاً' });
            }

            const { deliveryPersonId } = req.body;

            // التحقق من وجود مندوب التوصيل
            const deliveryPerson = await DeliveryPerson.findByPk(deliveryPersonId);
            if (!deliveryPerson) {
                return res.status(404).json({ success: false, message: 'مندوب التوصيل غير موجود' });
            }

            // إنشاء مهمة توصيل
            const delivery = await Delivery.create({
                orderId: order.id,
                deliveryPersonId: deliveryPersonId,
                status: 'assigned',
                assignedAt: new Date()
            });

            // تحديث حالة الطلب
            await order.update({ status: 'out_for_delivery' });

            // إرسال إشعار للعميل
            await Notification.create({
                customerId: order.customerId,
                title: 'طلبك في الطريق',
                message: `طلبك رقم #${order.id} في الطريق إليك مع مندوب التوصيل ${deliveryPerson.name}`,
                type: 'order_out_for_delivery',
                relatedId: order.id
            });

            res.json({
                success: true,
                message: 'تم تسليم الطلب لمندوب التوصيل بنجاح',
                delivery: {
                    id: delivery.id,
                    deliveryPerson: {
                        name: deliveryPerson.name,
                        phoneNumber: deliveryPerson.phoneNumber
                    }
                }
            });
        } catch (error) {
            console.error('Error assigning delivery:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء تسليم الطلب' });
        }
    }

    // تحديث حالة طلب
    async updateStatus(req, res) {
        try {
            const order = await Order.findByPk(req.params.id);

            if (!order) {
                return res.status(404).json({ success: false, message: 'الطلب غير موجود' });
            }

            const { status } = req.body;
            const validStatuses = ['pending', 'approved', 'rejected', 'preparing', 'out_for_delivery', 'delivered', 'cancelled'];

            if (!validStatuses.includes(status)) {
                return res.status(400).json({ success: false, message: 'حالة غير صحيحة' });
            }

            await order.update({ status });

            res.json({ success: true, message: 'تم تحديث حالة الطلب بنجاح' });
        } catch (error) {
            console.error('Error updating order status:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء تحديث الحالة' });
        }
    }
}

module.exports = new OrdersController();
