const { DeliveryPerson, Delivery } = require('../models');
const path = require('path');
// عرض جميع السائقين لمتجر معين
exports.index = async (req, res) => {
  const { storeId } = req.params;
  const limit = 20;
  const currentPage = parseInt(req.query.page) || 1;
  const offset = (currentPage - 1) * limit;

  try {
    const { count, rows: deliveryPeople } = await DeliveryPerson.findAndCountAll({
      where: { storeId },
      include: [{ model: Delivery, as: 'deliveries' }],
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });

    const totalPages = Math.ceil(count / limit);

    res.render('stores/deliveryPeople/index', {
      deliveryPeople,
      storeId,
      currentPage,
      totalPages
    });
  } catch (error) {
    console.error("Error fetching delivery people:", error);
    res.status(500).render('error', {
      error: { message: 'Unable to fetch delivery people' }
    });
  }
};

// عرض صفحة إضافة سائق
exports.createForm = (req, res) => {
  res.render('stores/deliveryPeople/create', { storeId: req.params.storeId });
};

// إنشاء سائق جديد
exports.create = async (req, res) => {
  const { storeId } = req.params;
  const { name, phoneNumber } = req.body;
  if (!req.files || !req.files.image) {
     return res.status(400).render('stores/deliveryPeople/create', {
        error: 'Please upload an image.'      });
    }
    const image = req.files.image;

    // تحقق من نوع الملف
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(image.mimetype)) {
      console.log('Invalid image format');
      return res.status(400).render('stores/deliveryPeople/create', {
        error: 'Invalid image format.'
      });
    }

    const fileName = `${Date.now()}-${image.name}`;
    const uploadPath = path.join(__dirname, '../public/uploads', fileName);

    // حفظ الصورة على السيرفر
    await image.mv(uploadPath);

  try {
    await DeliveryPerson.create({
      name,
      phoneNumber,
      storeId,
      image : fileName,
    });

    res.redirect(`/store/${storeId}/delivery-people`);
  } catch (error) {
    console.error(error);
    return res.status(400).render('stores/deliveryPeople/create', {
        error: 'حدث خطأ أثناء إضافة السائق' });
  }
};
// عرض صفحة تعديل سائق
exports.editForm = async (req, res) => {
  const deliveryPerson = await DeliveryPerson.findByPk(req.params.id);
  res.render('stores/deliveryPeople/edit', { deliveryPerson });
};

// تعديل بيانات سائق
exports.update = async (req, res) => {
  const { name, phoneNumber } = req.body;
  await DeliveryPerson.update({ name, phoneNumber }, { where: { id: req.params.id } });
  res.redirect(`/stores/${req.params.storeId}/delivery-people`);
};

exports.delete = async (req, res) => {
    const { id, storeId } = req.params;
    await DeliveryPerson.destroy({ where: { id, storeId } });
    res.redirect(`/stores/${storeId}/delivery-people`);
  };
