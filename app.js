require('dotenv').config({ path: '../.env' });
const express = require('express');
const cookieParser = require('cookie-parser');
const path = require('path');
const expressLayouts = require('express-ejs-layouts');
const helmet = require('helmet');
const cors = require('cors');
const compression = require('compression');
const morgan = require('morgan');
const { sequelize } = require('./models');
const logger = require('./utils/logger');
const errorHandler = require('./middleware/errorHandler');
const routes = require('./routes');
const NotificationScheduler = require('./utils/notificationScheduler');
const session = require('express-session');
const flash = require('connect-flash');


const app = express();

/** ======== Middlewares ======== **/

// Parse JSON and URL-encoded payloads with increased limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));



// Security headers
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "https://unpkg.com"],
        scriptSrcAttr: ["'self'", "'unsafe-inline'"], // <-- أضف هذا السطر
        styleSrc: [
          "'self'",
          "'unsafe-inline'",
          "https://unpkg.com",
          "https://fonts.googleapis.com",
          "https://cdnjs.cloudflare.com",
          "https://cdn.jsdelivr.net"
        ],
        imgSrc: ["'self'", "data:", "https://*.tile.openstreetmap.org"],
        connectSrc: ["'self'", "https://nominatim.openstreetmap.org"],
        fontSrc: [
          "'self'",
          "https://fonts.googleapis.com",
          "https://fonts.gstatic.com",
          "https://cdnjs.cloudflare.com",
          "https://cdn.jsdelivr.net"
        ],
      },
    },
  })
);




// CORS setup
app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
/*
// Rate limiting for /api
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 mins
    max: 100,
    message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);*/

// Request timeout (30 seconds)
app.use((req, res, next) => {
    req.setTimeout(30000, () => {
        const err = new Error('Request timeout');
        err.status = 408;
        next(err);
    });
    next();
});


// Response compression
app.use(compression());

// HTTP request logger
app.use(morgan('combined', { stream: { write: msg => logger.info(msg.trim()) } }));

// JSON and URL-encoded payloads already configured above

// Static files (adjust path if needed)
app.use(express.static(path.join(__dirname, 'public')));
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

app.use(session({
    secret: 'your_secret_key',   // استبدلها بمفتاح سري حقيقي
    resave: false,
    saveUninitialized: false
  }));

  app.use(flash());
// View engine setup (EJS + layouts)
app.use(expressLayouts);
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.set('layout', 'layouts/main');

// Cookie parser for JWT tokens
app.use(cookieParser());

// Make user data available in all views
app.use((req, res, next) => {
    const { extractToken, verifyToken } = require('./utils/jwt');

    try {
        const token = extractToken(req);
        if (token) {
            const decoded = verifyToken(token);
            res.locals.user = decoded;

            // For backward compatibility with existing views
            res.locals.session = {
                customerId: decoded.userType === 'customer' ? decoded.id : null,
                adminId: decoded.userType === 'admin' ? decoded.id : null,
                customerName: decoded.userType === 'customer' ? decoded.name : null,
                cart: req.cookies.cart ? JSON.parse(req.cookies.cart) : []
            };
        } else {
            res.locals.user = null;
            res.locals.session = {
                cart: req.cookies.cart ? JSON.parse(req.cookies.cart) : []
            };
        }
    } catch (error) {
        res.locals.user = null;
        res.locals.session = {
            cart: req.cookies.cart ? JSON.parse(req.cookies.cart) : []
        };
    }

    // Flash messages simulation (using query parameters for now)
    res.locals.success = req.query.success || '';
    res.locals.error = req.query.error || '';
    next();
});

/** ======== Routes ======== **/
// API routes for mobile app (Flutter)
//app.use('/api', require('./routes/api'));

// Main routes loader
app.use('/', routes);




// 404 handler (should be after all routes)
app.use((req, res) => {
    res.status(404).render('error', {
        error: {
            status: 404,
            message: 'Page not found'
        }
    });
});

// Centralized error handler (last middleware)
app.use(errorHandler);

/** ======== Server startup ======== **/

const PORT = process.env.PORT || 3000;

const startServer = async () => {
    try {
        await sequelize.authenticate();
        logger.info('Database connection established');

        // تفعيل مجدول الإشعارات
        NotificationScheduler.init();
        logger.info('Notification scheduler initialized');

        const server = app.listen(PORT, () => {
            logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
        });

        // Graceful shutdown
        const shutdown = async () => {
            logger.info('Shutting down server...');
            server.close(async () => {
                try {
                    await sequelize.close();
                    logger.info('Database connections closed');
                    process.exit(0);
                } catch (err) {
                    logger.error('Error during shutdown:', err);
                    process.exit(1);
                }
            });
        };

        process.on('SIGTERM', shutdown);
        process.on('SIGINT', shutdown);

    } catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
};

startServer();
