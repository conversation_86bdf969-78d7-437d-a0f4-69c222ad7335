<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> نظام إدارة المتاجر الذكي </title>

    <!-- Meta Tags for SEO -->
    <meta name="description" content="نظام إدارة المتاجر الذكي - منصة شاملة لإدارة المتاجر والطلبات والعملاء">
    <meta name="keywords" content="إدارة متاجر, نظام طلبات, إدارة عملاء, تجارة إلكترونية">
    <meta name="author" content="نظام إدارة المتاجر الذكي">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content=" نظام إدارة المتاجر الذكي ">
    <meta property="og:description" content="منصة شاملة لإدارة المتاجر والطلبات والعملاء">
    <meta property="og:type" content="website">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Animate.css -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <!-- Custom CSS الموحد -->
    <link href="/css/professional.css" rel="stylesheet">

   
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-gradient shadow-lg sticky-top">
        <div class="container">
            <!-- Brand -->
            <a href="/admin/dashboard" class="navbar-brand d-flex align-items-center">
                <div class="brand-icon me-2">
                    <i class="fas fa-store-alt"></i>
                </div>
                <div class="brand-text">
                    <span class="fw-bold">نظام إدارة المتاجر</span>
                    <small class="d-block text-light opacity-75">الذكي</small>
                </div>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler border-0 shadow-none" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- Main Navigation -->
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <% if (locals.session && locals.session.customerId) { %>
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern" href="/customers">
                                <i class="fas fa-home me-1"></i>
                                <span>الرئيسية</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern position-relative" href="/customers/cart">
                                <i class="fas fa-shopping-cart me-1"></i>
                                <span>السلة</span>
                                <span class="cart-badge position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                    0
                                </span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern" href="/customers/orders">
                                <i class="fas fa-box me-1"></i>
                                <span>طلباتي</span>
                            </a>
                        </li>
                    <% } else if (locals.session && locals.session.storeId) { %>
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern" href="/store/dashboard">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern" href="/store/products">
                                <i class="fas fa-boxes me-1"></i>
                                <span>المنتجات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern" href="/store/orders">
                                <i class="fas fa-clipboard-list me-1"></i>
                                <span>الطلبات</span>
                            </a>
                        </li>
                    <% } else if (locals.session && locals.session.adminId) { %>
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern" href="/admin/dashboard">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-modern dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-cogs me-1"></i>
                                <span>الإدارة</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern">
                                <li><a class="dropdown-item" href="/admin/customers"><i class="fas fa-users me-2"></i>العملاء</a></li>
                                <li><a class="dropdown-item" href="/admin/stores"><i class="fas fa-store me-2"></i>المتاجر</a></li>
                                <li><a class="dropdown-item" href="/admin/orders"><i class="fas fa-shopping-bag me-2"></i>الطلبات</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="/notifications"><i class="fas fa-bell me-2"></i>الإشعارات</a></li>
                            </ul>
                        </li>
                    <% } %>
                </ul>

                <!-- User Actions -->
                <ul class="navbar-nav">
                    <% if (locals.session && locals.session.customerId) { %>
                        <!-- Customer Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-modern position-relative" href="#" id="customerNotificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell fs-5"></i>
                                <span class="notification-badge position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="customerNotificationCount" style="display: none;">0</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern dropdown-menu-end">
                                <li class="dropdown-header text-center">
                                    <i class="fas fa-bell me-2"></i>الإشعارات
                                </li>
                                <div id="customerNotificationsList" class="notification-list"></div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center text-primary" href="/customers/notifications">
                                    <i class="fas fa-eye me-2"></i>عرض جميع الإشعارات
                                </a></li>
                            </ul>
                        </li>

                        <!-- Customer Profile -->
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-modern dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user-circle fs-5"></i>
                                </div>
                                <span>حسابي</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern dropdown-menu-end">
                                <li class="dropdown-header">
                                    <i class="fas fa-user me-2"></i>حساب العميل
                                </li>
                                <li><a class="dropdown-item" href="/customers/profile">
                                    <i class="fas fa-id-card me-2"></i>الملف الشخصي
                                </a></li>
                                <li><a class="dropdown-item" href="/customers/orders">
                                    <i class="fas fa-box me-2"></i>طلباتي
                                </a></li>
                                <li><a class="dropdown-item" href="/customers/notifications">
                                    <i class="fas fa-bell me-2"></i>الإشعارات
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="/customers/logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <% } else if (locals.session && locals.session.storeId) { %>
                        <!-- Store Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-modern position-relative" href="#" id="storeNotificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell fs-5"></i>
                                <span class="notification-badge position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="storeNotificationCount" style="display: none;">0</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern dropdown-menu-end">
                                <li class="dropdown-header text-center">
                                    <i class="fas fa-bell me-2"></i>الإشعارات
                                </li>
                                <div id="storeNotificationsList" class="notification-list"></div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center text-primary" href="/store/notifications">
                                    <i class="fas fa-eye me-2"></i>عرض جميع الإشعارات
                                </a></li>
                            </ul>
                        </li>

                        <!-- Store Profile -->
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-modern dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-store-alt fs-5"></i>
                                </div>
                                <span>متجري</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern dropdown-menu-end">
                                <li class="dropdown-header">
                                    <i class="fas fa-store me-2"></i>إدارة المتجر
                                </li>
                                <li><a class="dropdown-item" href="/store/profile">
                                    <i class="fas fa-info-circle me-2"></i>بيانات المتجر
                                </a></li>
                                <li><a class="dropdown-item" href="/store/products">
                                    <i class="fas fa-boxes me-2"></i>المنتجات
                                </a></li>
                                <li><a class="dropdown-item" href="/store/orders">
                                    <i class="fas fa-clipboard-list me-2"></i>الطلبات
                                </a></li>
                                <li><a class="dropdown-item" href="/store/notifications">
                                    <i class="fas fa-bell me-2"></i>الإشعارات
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="/store/logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <% } else if (locals.session && locals.session.adminId) { %>
                        <!-- Admin Notifications -->
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-modern position-relative" href="#" id="adminNotificationDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell fs-5"></i>
                                <span class="notification-badge position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" id="adminNotificationCount" style="display: none;">0</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern dropdown-menu-end">
                                <li class="dropdown-header text-center">
                                    <i class="fas fa-bell me-2"></i>الإشعارات
                                </li>
                                <div id="adminNotificationsList" class="notification-list"></div>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center text-primary" href="/notifications">
                                    <i class="fas fa-eye me-2"></i>عرض جميع الإشعارات
                                </a></li>
                            </ul>
                        </li>

                        <!-- Admin Profile -->
                        <li class="nav-item dropdown">
                            <a class="nav-link nav-link-modern dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar me-2">
                                    <i class="fas fa-user-shield fs-5"></i>
                                </div>
                                <span>المدير</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-modern dropdown-menu-end">
                                <li class="dropdown-header">
                                    <i class="fas fa-crown me-2"></i>لوحة الإدارة
                                </li>
                                <li><a class="dropdown-item" href="/admin/dashboard">
                                    <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                </a></li>
                                <li><a class="dropdown-item" href="/notifications">
                                    <i class="fas fa-bell me-2"></i>الإشعارات
                                </a></li>
                                <li><a class="dropdown-item" href="/admin/auth/change-password">
                                    <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="/admin/auth/logout">
                                    <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                </a></li>
                            </ul>
                        </li>
                    <% } else { %>
                        <!-- Guest Actions -->
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern" href="/customers/auth/login">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                <span>تسجيل الدخول</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link nav-link-modern btn btn-outline-light btn-sm ms-2" href="/customers/auth/register">
                                <i class="fas fa-user-plus me-1"></i>
                                <span>إنشاء حساب</span>
                            </a>
                        </li>
                    <% } %>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="container-fluid mt-3">
        <% if (locals.success && success.length > 0) { %>
            <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeInDown">
                <i class="fas fa-check-circle me-2"></i>
                <%= success %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
        <% if (locals.error && error.length > 0) { %>
            <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeInDown">
                <i class="fas fa-exclamation-circle me-2"></i>
                <%= error %>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <% } %>
    </div>

    <!-- Main Content -->
    <main>
        <%- body %>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-store"></i> نظام إدارة المتاجر الذكي</h5>
                    <p>منصة شاملة لإدارة المتاجر والطلبات والعملاء</p>
                </div>
                <div class="col-md-3">
                    <h5>روابط سريعة</h5>
                    <ul class="list-unstyled">
                        <li><a href="/about" class="text-white-50">من نحن</a></li>
                        <li><a href="/contact" class="text-white-50">اتصل بنا</a></li>
                        <li><a href="/terms" class="text-white-50">الشروط والأحكام</a></li>
                        <li><a href="/privacy" class="text-white-50">سياسة الخصوصية</a></li>
                    </ul>
                </div>
                <div class="col-md-3">
                    <h5>تواصل معنا</h5>
                    <div class="social-links">
                        <a href="#" class="text-white-50 me-3"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-white-50 me-3"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-white-50 me-3"><i class="fab fa-instagram fa-lg"></i></a>
                        <a href="#" class="text-white-50"><i class="fab fa-linkedin fa-lg"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; <%= new Date().getFullYear() %> نظام إدارة المتاجر الذكي. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
     <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

   
    <!-- Custom JS -->
    <script src="/js/main.js"></script>
    <script src="/js/main-new.js"></script>

    <script>
        // Hide loading overlay immediately and when page loads
        document.addEventListener('DOMContentLoaded', function() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.opacity = '0';
                setTimeout(() => {
                    loadingOverlay.style.display = 'none';
                }, 100);
            }
        });

        // Also hide on window load as backup
        window.addEventListener('load', function() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                try {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                } catch (e) {
                    // Fallback if bootstrap is not loaded
                    alert.style.display = 'none';
                }
            });
        }, 5000);

        // تحسينات القائمة التفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التمرير على القائمة
            window.addEventListener('scroll', function() {
                const navbar = document.querySelector('.navbar');
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // إغلاق القائمة المنسدلة عند النقر خارجها
            document.addEventListener('click', function(e) {
                const dropdowns = document.querySelectorAll('.dropdown-menu.show');
                dropdowns.forEach(dropdown => {
                    if (!dropdown.contains(e.target) && !dropdown.previousElementSibling.contains(e.target)) {
                        const bsDropdown = bootstrap.Dropdown.getInstance(dropdown.previousElementSibling);
                        if (bsDropdown) {
                            bsDropdown.hide();
                        }
                    }
                });
            });

            // تحديث عداد السلة (إذا كان موجود)
            function updateCartCount() {
                const cartBadge = document.querySelector('.cart-badge');
                if (cartBadge) {
                    // يمكن تحديث هذا من localStorage أو API
                    const cartItems = JSON.parse(localStorage.getItem('cart') || '[]');
                    const totalItems = cartItems.reduce((sum, item) => sum + (item.quantity || 1), 0);
                    cartBadge.textContent = totalItems;
                    cartBadge.style.display = totalItems > 0 ? 'flex' : 'none';
                }
            }

            // تحديث عداد الإشعارات
            function updateNotificationCount() {
                // تحديث عداد الإشعارات للعملاء
                const customerBadge = document.getElementById('customerNotificationCount');
                if (customerBadge) {
                    fetch('/notifications/unread?userType=customer')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.count > 0) {
                                customerBadge.textContent = data.count;
                                customerBadge.style.display = 'flex';
                            } else {
                                customerBadge.style.display = 'none';
                            }
                        })
                        .catch(error => console.log('Error updating customer notifications:', error));
                }

                // تحديث عداد الإشعارات للمتاجر
                const storeBadge = document.getElementById('storeNotificationCount');
                if (storeBadge) {
                    fetch('/notifications/unread?userType=store')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.count > 0) {
                                storeBadge.textContent = data.count;
                                storeBadge.style.display = 'flex';
                            } else {
                                storeBadge.style.display = 'none';
                            }
                        })
                        .catch(error => console.log('Error updating store notifications:', error));
                }

                // تحديث عداد الإشعارات للإدارة
                const adminBadge = document.getElementById('adminNotificationCount');
                if (adminBadge) {
                    fetch('/notifications/unread?userType=admin')
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.count > 0) {
                                adminBadge.textContent = data.count;
                                adminBadge.style.display = 'flex';
                            } else {
                                adminBadge.style.display = 'none';
                            }
                        })
                        .catch(error => console.log('Error updating admin notifications:', error));
                }
            }

            // تشغيل التحديثات
            updateCartCount();
            updateNotificationCount();

            // تحديث دوري كل 30 ثانية
            setInterval(updateNotificationCount, 30000);

            // تحديث عداد السلة عند تغيير localStorage
            window.addEventListener('storage', function(e) {
                if (e.key === 'cart') {
                    updateCartCount();
                }
            });

            // تأثيرات بصرية للروابط
            const navLinks = document.querySelectorAll('.nav-link-modern');
            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                link.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.transform = 'translateY(0)';
                    }
                });
            });

            // تحديد الرابط النشط
            const currentPath = window.location.pathname;
            navLinks.forEach(link => {
                if (link.getAttribute('href') === currentPath) {
                    link.classList.add('active');
                }
            });
        });
    </script>
</body>
</html>
