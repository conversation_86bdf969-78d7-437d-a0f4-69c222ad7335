const BaseController = require('./BaseController');
const { Product, Image, Category } = require('../models');
const path = require('path');

class ProductsController extends BaseController {
    constructor() {
        super(Product, 'products');
    }

    async index(req, res) {
        try {
            const products = await Product.sequelize.transaction(async (t) => {
                return await Product.findAll({
                    include: [
                        {
                            model: Category,
                            as: 'category',
                            required: false,
                            attributes: ['id', 'name']
                        },
                        {
                            model: Image,
                            as: 'images',
                            required: false,
                            attributes: ['id', 'image']
                        }
                    ],
                    where: { status: 'active' },
                    attributes: [
                        'id', 'name', 'description', 'price',
                        'quantity', 'categoryId', 'status'
                    ],
                    order: [['createdAt', 'DESC']],
                    transaction: t
                });
            });

            res.render('admin/products/index', {
                products,
                title: 'إدارة المنتجات'
            });

        } catch (error) {
            console.error('Error in ProductsController.index:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while fetching products',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async create(req, res) {
        try {
            const categories = await Category.findAll({
                where: { status: 'active' },
                attributes: ['id', 'name']
            });

            res.render('admin/products/create', { 
                categories,
                title: 'إضافة منتج جديد'
            });
        } catch (error) {
            console.error('Error in ProductsController.create:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while loading the create form',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async store(req, res) {
        try {
            const { name, price, categoryId, description, quantity, barcode } = req.body;
            if (!name || !price || !categoryId) {
                const categories = await Category.findAll({
                    where: { status: 'active' },
                    attributes: ['id', 'name']
                });
                
                return res.status(400).render('admin/products/create', {
                    error: 'جميع الحقول مطلوبة',
                    categories,
                    formData: req.body
                });
            }


            // إنشاء المنتج
            const product = await Product.create({
                name,
                price: parseFloat(price),
                categoryId: parseInt(categoryId),
                description: description || '',
                quantity: parseInt(quantity) || 0,
                barcode: barcode || null,
                status: 'active'
            });

            // معالجة رفع الصور
            if (req.files && req.files.length > 0) {
                for (const file of req.files) {
                    await Image.create({
                        productId: product.id,
                        image: `/uploads/products/${file.filename}`
                    });
                }
            }
 
            req.flash('success', 'تم إنشاء المنتج بنجاح');
            res.redirect('/admin/products');
        } catch (error) {
            console.error('Error in ProductsController.store:', error);
            const categories = await Category.findAll({
                where: { status: 'active' },
                attributes: ['id', 'name']
            });
            
            res.status(500).render('admin/products/create', {
                error: 'حدث خطأ أثناء إنشاء المنتج',
                categories,
                formData: req.body
            });
        }
    }

    async show(req, res) {
        try {
            const product = await Product.findByPk(req.params.id, {
                include: [
                    {
                        model: Category,
                        as: 'category',
                        attributes: ['id', 'name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['id', 'image']
                    }
                ]
            });

            if (!product) {
                return res.status(404).render('error', {
                    error: { message: 'المنتج غير موجود' }
                });
            }

            res.render('admin/products/show', { 
                product,
                title: `تفاصيل المنتج: ${product.name}`
            });
        } catch (error) {
            console.error('Error in ProductsController.show:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while fetching the product',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async edit(req, res) {
        try {
            const [product, categories] = await Promise.all([
                Product.findByPk(req.params.id, {
                    include: [
                        {
                            model: Category,
                            as: 'category',
                            attributes: ['id', 'name']
                        },
                        {
                            model: Image,
                            as: 'images',
                            attributes: ['id', 'image']
                        }
                    ]
                }),
                Category.findAll({
                    where: { status: 'active' },
                    attributes: ['id', 'name']
                })
            ]);

            if (!product) {
                return res.status(404).render('error', {
                    error: { message: 'المنتج غير موجود' }
                });
            }

            res.render('admin/products/edit', { 
                product, 
                categories,
                title: `تعديل المنتج: ${product.name}`
            });
        } catch (error) {
            console.error('Error in ProductsController.edit:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while loading the edit form',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async update(req, res) {
        try {
            const product = await Product.findByPk(req.params.id);
            if (!product) {
                return res.status(404).render('error', {
                    error: { message: 'المنتج غير موجود' }
                });
            }

            const { name, price, categoryId, description, quantity, barcode } = req.body;
            
            await product.update({
                name,
                price: parseFloat(price),
                categoryId: parseInt(categoryId),
                description: description || '',
                quantity: parseInt(quantity) || 0,
                barcode: barcode || null
            });
console.log("dsfgdg",req.files);
            // رفع الصور الجديدة
            if (req.files && req.files.length > 0) {
                for (const file of req.files) {
                    await Image.create({
                        productId: product.id,
                        image: `/uploads/products/${file.filename}`
                    });
                }
            }

            req.flash('success', 'تم تحديث المنتج بنجاح');
            res.redirect('/admin/products');
        } catch (error) {
            console.error('Error in ProductsController.update:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while updating the product',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    async destroy(req, res) {
        try {
            const product = await Product.findByPk(req.params.id);
            if (!product) {
                return res.status(404).render('error', {
                    error: { message: 'المنتج غير موجود' }
                });
            }

            // حذف ناعم - تغيير الحالة إلى inactive
            await product.update({ status: 'inactive' });

            req.flash('success', 'تم حذف المنتج بنجاح');
            res.redirect('/admin/products');
        } catch (error) {
            console.error('Error in ProductsController.destroy:', error);
            res.status(500).render('error', {
                error: {
                    message: 'An error occurred while deleting the product',
                    details: process.env.NODE_ENV === 'development' ? error.message : null
                }
            });
        }
    }

    // API: جلب المنتجات للعملاء
    async getProducts(req, res) {
        try {
            const { page = 1, limit = 10, search = '', categoryId = null } = req.query;
            const offset = (page - 1) * limit;
            
            const whereClause = { status: 'active' };
            
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }
            
            if (categoryId) {
                whereClause.categoryId = categoryId;
            }

            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Category,
                        as: 'category',
                        attributes: ['id', 'name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['id', 'image']
                    }
                ],
                attributes: ['id', 'name', 'description', 'price', 'quantity'],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            res.json({
                success: true,
                message: 'تم جلب المنتجات بنجاح',
                data: {
                    products,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في جلب المنتجات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    // حذف صورة منتج
    async deleteImage(req, res) {
        try {
            const imageId = req.params.id;
            const image = await Image.findByPk(imageId);

            if (!image) {
                return res.status(404).json({
                    success: false,
                    message: 'الصورة غير موجودة'
                });
            }

            // حذف الملف من النظام
            const fs = require('fs');
            const imagePath = path.join(__dirname, '..', 'public', image.image);
            if (fs.existsSync(imagePath)) {
                fs.unlinkSync(imagePath);
            }

            // حذف السجل من قاعدة البيانات
            await image.destroy();

            res.json({
                success: true,
                message: 'تم حذف الصورة بنجاح'
            });

        } catch (error) {
            console.error('Error deleting image:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ أثناء حذف الصورة'
            });
        }
    }
}

module.exports = new ProductsController();
