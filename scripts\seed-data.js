const { Admin, Category, Customer } = require('../models');
const bcrypt = require('bcrypt');

async function seedData() {
    try {
        console.log('بدء إضافة البيانات التجريبية...');

        // إضافة مدير تجريبي
        const hashedPassword = await bcrypt.hash('admin123', 10);
        
        const admin = await Admin.findOrCreate({
            where: { email: '<EMAIL>' },
            defaults: {
                username: 'admin',
                fullName: 'مدير النظام',
                email: '<EMAIL>',
                password: hashedPassword,
                role: 'super_admin',
                status: 'active'
            }
        });

        console.log('تم إضافة المدير:', admin[0].fullName);

        // إضافة فئات تجريبية
        const categories = [
            {
                name: 'إلكترونيات',
                description: 'أجهزة إلكترونية ومعدات تقنية',
                status: 'active'
            },
            {
                name: 'ملاب<PERSON>',
                description: 'ملابس رجالية ونسائية وأطفال',
                status: 'active'
            },
            {
                name: 'منزل ومطبخ',
                description: 'أدوات منزلية ومطبخية',
                status: 'active'
            }
        ];

        for (const categoryData of categories) {
            const category = await Category.findOrCreate({
                where: { name: categoryData.name },
                defaults: categoryData
            });
            console.log('تم إضافة الفئة:', category[0].name);
        }

        // إضافة عميل تجريبي
        const customerPassword = await bcrypt.hash('customer123', 10);

        const customer = await Customer.findOrCreate({
            where: { phoneNumber: '0501234567' },
            defaults: {
                name: 'أحمد محمد',
                phoneNumber: '0501234567',
                password: customerPassword,
                status: 'active',
                discountRate: 0
            }
        });

        console.log('تم إضافة العميل:', customer[0].name);

        console.log('تم إنجاز إضافة البيانات التجريبية بنجاح!');
        console.log('\nبيانات تسجيل الدخول:');
        console.log('المدير: <EMAIL> / admin123');
        console.log('العميل: 0501234567 / customer123');

    } catch (error) {
        console.error('خطأ في إضافة البيانات:', error);
    }
}

// تشغيل الدالة إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    seedData().then(() => {
        process.exit(0);
    }).catch(error => {
        console.error(error);
        process.exit(1);
    });
}

module.exports = seedData;
