const bcrypt = require('bcryptjs');
const { Sequelize, DataTypes } = require('sequelize');


const sequelize = new Sequelize('DBCompanytest', 'IT', '123', {
    host: 'localhost',
    dialect: 'mssql',
    dialectOptions: {
      options: {
        encrypt: false // تعطيل التشفير (SSL)
      }
    },
    logging: false,
  });

// تعريف موديل الـ Admin (عدل حسب موديلك الفعلي)
const Admin = sequelize.define('Admin', {
  username: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  role: {
    type: DataTypes.STRING,
    defaultValue: 'admin',
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  fullName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  status: {
    type: DataTypes.STRING,
    defaultValue: 'active',
  },
  lastLogin: {
    type: DataTypes.DATE,
    allowNull: true,
  }
}, {
  tableName: 'Admins', // تأكد من اسم الجدول الصحيح
  timestamps: false,
});

async function addAdmin(username, plainPassword) {
  try {
    await sequelize.authenticate();
    console.log('Connection has been established successfully.');

    // توليد هاش كلمة السر
    const hashedPassword = bcrypt.hashSync(plainPassword, 10);

    // إنشاء أدمن جديد
    const newAdmin = await Admin.create({
      username,
      password: hashedPassword,
      role: 'admin', // أو 'super_admin' حسب ما تريد
      email: '<EMAIL>',
      fullName: 'admin',
      status: 'active',
      lastLogin: new Date()
    });

    console.log('New admin created:', newAdmin.username);
    process.exit(0);
  } catch (error) {
    console.error('Error adding admin:', error);
    process.exit(1);
  }
}

// استدعاء الدالة مع بياناتك
const username = 'admin';       // عدّل اسم المستخدم
const password = 'admin';  // عدّل كلمة السر

addAdmin(username, password);
