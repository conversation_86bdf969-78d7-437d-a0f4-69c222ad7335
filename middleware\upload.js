const multer = require('multer');
const path = require('path');
const fs = require('fs');

// إنشاء مجلد uploads إذا لم يكن موجود
const createUploadDir = (dir) => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
    }
};

// إعداد التخزين للمنتجات
const productStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadPath = path.join(__dirname, '..', 'public', 'uploads', 'products');
        createUploadDir(uploadPath);
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // إنشاء اسم فريد للملف
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const extension = path.extname(file.originalname);
        cb(null, 'product-' + uniqueSuffix + extension);
    }
});

// إعداد التخزين للعملاء
const customerStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadPath = path.join(__dirname, '..', 'public', 'uploads', 'customers');
        createUploadDir(uploadPath);
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        // إنشاء اسم فريد للملف
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const extension = path.extname(file.originalname);
        cb(null, 'customer-' + uniqueSuffix + extension);
    }
});

// فلتر الملفات - قبول الصور فقط
const fileFilter = (req, file, cb) => {
    // قبول الصور فقط
    if (file.mimetype.startsWith('image/')) {
        cb(null, true);
    } else {
        cb(new Error('يجب أن يكون الملف صورة'), false);
    }
};

// إعداد multer للمنتجات
const uploadProducts = multer({
    storage: productStorage,
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
        files: 10 // حد أقصى 10 صور
    },
    fileFilter: fileFilter
});

// إعداد multer للعملاء
const uploadCustomers = multer({
    storage: customerStorage,
    limits: {
        fileSize: 2 * 1024 * 1024, // 2MB
        files: 1 // صورة واحدة فقط
    },
    fileFilter: fileFilter
});

// معالجة أخطاء multer
const handleMulterError = (error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: 'حجم الملف كبير جداً'
            });
        }
        if (error.code === 'LIMIT_FILE_COUNT') {
            return res.status(400).json({
                success: false,
                message: 'عدد الملفات كبير جداً'
            });
        }
        if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            return res.status(400).json({
                success: false,
                message: 'حقل الملف غير متوقع'
            });
        }
    }

    if (error.message === 'يجب أن يكون الملف صورة') {
        return res.status(400).json({
            success: false,
            message: error.message
        });
    }

    next(error);
};

module.exports = {
    uploadProducts,
    uploadCustomers,
    handleMulterError,
    // للتوافق مع الكود القديم
    upload: uploadCustomers.single('image')
};
