<h1 class="mb-4">تعديل التوصيل رقم <%= delivery.id %></h1>

<form action="/store/deliveries/<%= delivery.id %>" method="POST" class="card p-4 shadow-sm">

  <div class="mb-3">
    <label for="status" class="form-label">حالة التوصيل</label>
    <select name="status" id="status" class="form-select">
      <% 
        const statusMap = {
          pending: 'قيد الانتظار',
          in_transit: 'قيد التوصيل',
          delivered: 'تم التوصيل',
          cancelled: 'أُلغي'
        };
        ['pending', 'in_transit', 'delivered', 'cancelled'].forEach(s => { 
      %>
        <option value="<%= s %>" <%= delivery.status === s ? 'selected' : '' %>>
          <%= statusMap[s] %>
        </option>
      <% }) %>
    </select>
  </div>

  <div class="mb-3">
    <label for="pickupTime" class="form-label">وقت الاستلام</label>
    <input type="datetime-local" name="pickupTime" id="pickupTime" class="form-control"
      value="<%= delivery.pickupTime ? delivery.pickupTime.toISOString().slice(0, 16) : '' %>">
  </div>

  <div class="mb-3">
    <label for="deliveryTime" class="form-label">وقت التوصيل</label>
    <input type="datetime-local" name="deliveryTime" id="deliveryTime" class="form-control"
      value="<%= delivery.deliveryTime ? delivery.deliveryTime.toISOString().slice(0, 16) : '' %>">
  </div>

  <div class="mb-3">
    <label for="notes" class="form-label">ملاحظات</label>
    <textarea name="notes" id="notes" class="form-control" rows="3"><%= delivery.notes || '' %></textarea>
  </div>

  <div class="d-flex justify-content-between">
    <a href="/admin/deliveries" class="btn btn-secondary">
      <i class="fas fa-arrow-left"></i> العودة
    </a>
    <button type="submit" class="btn btn-success">
      <i class="fas fa-save"></i> تحديث
    </button>
  </div>
</form>
