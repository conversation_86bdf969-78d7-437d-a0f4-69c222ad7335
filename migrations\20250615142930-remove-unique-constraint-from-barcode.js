'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // إزالة قيد unique من حقل barcode
    await queryInterface.removeConstraint('Products', 'UQ__Products__C16E36F8DD4F40C1');
  },

  async down (queryInterface, Sequelize) {
    // إعادة إضافة قيد unique لحقل barcode
    await queryInterface.addConstraint('Products', {
      fields: ['barcode'],
      type: 'unique',
      name: 'UQ__Products__C16E36F8DD4F40C1'
    });
  }
};
