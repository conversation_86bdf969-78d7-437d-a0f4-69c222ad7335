const { Notification, Customer, Store, Admin } = require('../models');
const NotificationService = require('../services/NotificationService');
const logger = require('../utils/logger');

async function seedNotifications() {
    try {
        console.log('🌱 بدء إنشاء إشعارات تجريبية...');

        // إنشاء إشعارات تجريبية متنوعة
        const sampleNotifications = [
            {
                title: 'مرحباً بك في النظام',
                message: 'نرحب بك في نظام إدارة المتاجر الإلكترونية. نتمنى لك تجربة ممتعة ومفيدة.',
                type: 'success',
                priority: 'normal',
                actionUrl: '/dashboard',
                actionText: 'استكشاف النظام'
            },
            {
                title: 'تحديث النظام',
                message: 'تم تحديث النظام إلى الإصدار الجديد مع ميزات محسنة وإصلاحات أمنية.',
                type: 'info',
                priority: 'normal',
                actionUrl: '/changelog',
                actionText: 'عرض التحديثات'
            },
            {
                title: 'عرض خاص - خصم 20%',
                message: 'احصل على خصم 20% على جميع المنتجات لفترة محدودة. لا تفوت الفرصة!',
                type: 'promotion',
                priority: 'high',
                actionUrl: '/promotions',
                actionText: 'استفد من العرض',
                expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // ينتهي خلال أسبوع
            },
            {
                title: 'تنبيه أمني',
                message: 'تم اكتشاف محاولة دخول غير مصرح بها لحسابك. يرجى مراجعة إعدادات الأمان.',
                type: 'error',
                priority: 'urgent',
                actionUrl: '/security',
                actionText: 'مراجعة الأمان'
            },
            {
                title: 'طلب جديد',
                message: 'لديك طلب جديد رقم #12345 يحتاج للمعالجة.',
                type: 'order',
                priority: 'high',
                actionUrl: '/orders/12345',
                actionText: 'معالجة الطلب'
            },
            {
                title: 'تحذير: مخزون منخفض',
                message: 'المنتج "هاتف ذكي" لديه مخزون منخفض (5 قطع متبقية).',
                type: 'warning',
                priority: 'high',
                actionUrl: '/products/inventory',
                actionText: 'تحديث المخزون'
            },
            {
                title: 'صيانة مجدولة',
                message: 'سيتم إجراء صيانة مجدولة للنظام غداً من الساعة 2:00 إلى 4:00 صباحاً.',
                type: 'system',
                priority: 'normal',
                expiresAt: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000) // ينتهي خلال يومين
            },
            {
                title: 'تم تأكيد طلبك',
                message: 'تم تأكيد طلبك رقم #12346 وسيتم تحضيره قريباً.',
                type: 'success',
                priority: 'normal',
                actionUrl: '/orders/12346',
                actionText: 'تتبع الطلب'
            },
            {
                title: 'رسالة من الدعم الفني',
                message: 'شكراً لتواصلك معنا. تم حل مشكلتك بنجاح. نتمنى لك تجربة أفضل.',
                type: 'info',
                priority: 'low',
                actionUrl: '/support',
                actionText: 'مركز الدعم'
            },
            {
                title: 'تقرير المبيعات الشهري',
                message: 'تقرير المبيعات لشهر ديسمبر متاح الآن للمراجعة.',
                type: 'system',
                priority: 'normal',
                actionUrl: '/reports/monthly',
                actionText: 'عرض التقرير'
            }
        ];

        // جلب المستخدمين الموجودين
        const [customers, stores, admins] = await Promise.all([
            Customer.findAll({ limit: 3 }),
            Store.findAll({ limit: 3 }),
            Admin.findAll({ limit: 3 })
        ]);

        console.log(`📊 تم العثور على ${customers.length} عملاء، ${stores.length} متاجر، ${admins.length} مشرفين`);

        let notificationCount = 0;

        // إنشاء إشعارات للمشرفين
        for (const admin of admins) {
            for (let i = 0; i < sampleNotifications.length; i++) {
                const notification = sampleNotifications[i];
                await NotificationService.createNotification({
                    ...notification,
                    adminId: admin.id
                });
                notificationCount++;

                // جعل بعض الإشعارات مقروءة عشوائياً
                if (Math.random() > 0.6) {
                    await Notification.update(
                        { readAt: new Date() },
                        { 
                            where: { 
                                adminId: admin.id,
                                title: notification.title
                            }
                        }
                    );
                }
            }
        }

        // إنشاء إشعارات للعملاء
        for (const customer of customers) {
            const customerNotifications = sampleNotifications.filter(n => 
                ['success', 'info', 'promotion', 'order'].includes(n.type)
            );

            for (const notification of customerNotifications) {
                await NotificationService.createNotification({
                    ...notification,
                    customerId: customer.id
                });
                notificationCount++;

                // جعل بعض الإشعارات مقروءة عشوائياً
                if (Math.random() > 0.7) {
                    await Notification.update(
                        { readAt: new Date() },
                        { 
                            where: { 
                                customerId: customer.id,
                                title: notification.title
                            }
                        }
                    );
                }
            }
        }

        // إنشاء إشعارات للمتاجر
        for (const store of stores) {
            const storeNotifications = sampleNotifications.filter(n => 
                ['order', 'warning', 'system', 'info'].includes(n.type)
            );

            for (const notification of storeNotifications) {
                await NotificationService.createNotification({
                    ...notification,
                    storeId: store.id
                });
                notificationCount++;

                // جعل بعض الإشعارات مقروءة عشوائياً
                if (Math.random() > 0.5) {
                    await Notification.update(
                        { readAt: new Date() },
                        { 
                            where: { 
                                storeId: store.id,
                                title: notification.title
                            }
                        }
                    );
                }
            }
        }

        // إنشاء بعض الإشعارات العامة (بدون مستقبل محدد)
        const generalNotifications = [
            {
                title: 'إعلان عام',
                message: 'نعلن عن إطلاق ميزات جديدة في النظام. ترقبوا المزيد من التحديثات.',
                type: 'system',
                priority: 'normal'
            },
            {
                title: 'عطلة رسمية',
                message: 'سيكون النظام متاحاً بشكل محدود خلال العطلة الرسمية القادمة.',
                type: 'info',
                priority: 'low',
                expiresAt: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
            }
        ];

        for (const notification of generalNotifications) {
            await NotificationService.createNotification(notification);
            notificationCount++;
        }

        console.log(`✅ تم إنشاء ${notificationCount} إشعار تجريبي بنجاح!`);

        // إحصائيات
        const stats = await NotificationService.getNotificationStats();
        console.log('📈 إحصائيات الإشعارات:');
       // console.table(stats);

        return notificationCount;

    } catch (error) {
        console.error('❌ خطأ في إنشاء الإشعارات التجريبية:', error);
        throw error;
    }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
    seedNotifications()
        .then((count) => {
            console.log(`🎉 تم الانتهاء من إنشاء ${count} إشعار تجريبي`);
            process.exit(0);
        })
        .catch((error) => {
            console.error('💥 فشل في إنشاء الإشعارات التجريبية:', error);
            process.exit(1);
        });
}

module.exports = seedNotifications;
