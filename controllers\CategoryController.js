const BaseController = require('./BaseController');
const { Category, Product, sequelize } = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { Op } = require('sequelize');

class CategoryController extends BaseController {
    constructor() {
        super(Category, 'categories');
    }

    // عرض قائمة الفئات
    async index(req, res) {
        try {
            // جلب البيانات بدون include معقد
            const { count, rows: categories } = await Category.findAndCountAll({
                order: [['name', 'ASC']],
                limit: 50,
                offset: 0
            });

            res.render('admin/categories/index', {
                categories,
                pagination: {
                    currentPage: 1,
                    totalPages: Math.ceil(count / 50),
                    totalItems: count
                },
                filters: {},
                currentUrl: req.originalUrl,
                originalUrl: req.originalUrl,
                activeFiltersCount: 0
            });
        } catch (error) {
            console.error('Error fetching categories:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء جلب الفئات' } });
        }
    }

    // عرض نموذج إضافة فئة جديدة
    async create(req, res) {
        try {
            res.render('admin/categories/create', {
                title: 'إضافة فئة جديدة'
            });
        } catch (error) {
            console.error('Error loading create category form:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل النموذج' } });
        }
    }

    // حفظ فئة جديدة
    async store(req, res) {
        try {
            const { name, description, status } = req.body;
            // التحقق من البيانات المطلوبة
            if (!name) {
                return res.status(400).render('admin/categories/create', {
                    error: 'اسم الفئة مطلوب',
                    formData: req.body
                });
            }

            // التحقق من عدم وجود فئة بنفس الاسم
            const existingCategory = await Category.findOne({ where: { name } });
            if (existingCategory) {
                return res.status(400).render('admin/categories/create', {
                    error: 'يوجد فئة بهذا الاسم مسبقاً',
                    formData: req.body
                });
            }
            let imagePath = null;
            // رفع الصور الجديدة
            if (req.file) {
                imagePath = `/uploads/categories/${req.file.filename}`;
            }

            // إنشاء الفئة
            await Category.create({
                name,
                description: description || null,
                status: status || 'active',
                image: imagePath

            });

            req.flash('success', 'تم إضافة الفئة بنجاح');
            res.redirect('/admin/categories');
        } catch (error) {
            console.error('Error creating category:', error);
            res.status(500).render('admin/categories/create', {
                error: 'حدث خطأ أثناء إضافة الفئة',
                formData: req.body
            });
        }
    }

    // عرض تفاصيل فئة
    async show(req, res) {
        try {
            const category = await Category.findByPk(req.params.id);

            if (!category) {
                return res.status(404).render('error', { error: { message: 'الفئة غير موجودة' } });
            }

            res.render('admin/categories/show', { 
                category,
                title: `تفاصيل الفئة: ${category.name}`
            });
        } catch (error) {
            console.error('Error fetching category details:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء جلب تفاصيل الفئة' } });
        }
    }

    // عرض نموذج تعديل فئة
    async edit(req, res) {
        try {
            const category = await Category.findByPk(req.params.id);
            
            if (!category) {
                return res.status(404).render('error', { error: { message: 'الفئة غير موجودة' } });
            }

            res.render('admin/categories/edit', { 
                category,
                title: `تعديل الفئة: ${category.name}`
            });
        } catch (error) {
            console.error('Error loading edit category form:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل النموذج' } });
        }
    }

    // تحديث بيانات فئة
    async update(req, res) {
        try {
            const category = await Category.findByPk(req.params.id);
            
            if (!category) {
                return res.status(404).render('error', { error: { message: 'الفئة غير موجودة' } });
            }

            const { name, description, status } = req.body;

            // التحقق من عدم وجود فئة أخرى بنفس الاسم
            if (name !== category.name) {
                const existingCategory = await Category.findOne({ 
                    where: { 
                        name,
                        id: { [Op.ne]: category.id }
                    }
                });
                
                if (existingCategory) {
                    return res.status(400).render('admin/categories/edit', {
                        error: 'يوجد فئة أخرى بهذا الاسم',
                        category: { ...category.toJSON(), ...req.body }
                    });
                }
            }

            // معالجة رفع الصورة الجديدة
            let imagePath = category.image;
            if (req.file) {
                imagePath = `/uploads/categories/${req.file.filename}`;
            }

            // تحديث البيانات
            await category.update({
                name,
                description: description || null,
                image: imagePath,
                status
            });

            req.flash('success', 'تم تحديث الفئة بنجاح');
            res.redirect('/admin/categories');
        } catch (error) {
            console.error('Error updating category:', error);
            res.status(500).render('admin/categories/edit', {
                error: 'حدث خطأ أثناء تحديث الفئة',
                category: { ...req.body, id: req.params.id }
            });
        }
    }

    // حذف فئة
    async delete(req, res) {
        try {
            const category = await Category.findByPk(req.params.id);
            
            if (!category) {
                return res.status(404).render('error', { error: { message: 'الفئة غير موجودة' } });
            }

            // حذف الفئة
            await category.destroy();

            req.flash('success', 'تم حذف الفئة بنجاح');
            res.redirect('/admin/categories');
        } catch (error) {
            console.error('Error deleting category:', error);
            req.flash('error', 'حدث خطأ أثناء حذف الفئة');
            res.redirect('/admin/categories');
        }
    }

    // تحديث حالة فئة
    async updateStatus(req, res) {
        try {
            const category = await Category.findByPk(req.params.id);
            
            if (!category) {
                return res.status(404).json({ success: false, message: 'الفئة غير موجودة' });
            }

            const { status } = req.body;
            
            if (!['active', 'inactive'].includes(status)) {
                return res.status(400).json({ success: false, message: 'حالة غير صحيحة' });
            }

            await category.update({ status });

            res.json({ success: true, message: `تم تحديث حالة الفئة إلى ${status}` });
        } catch (error) {
            console.error('Error updating category status:', error);
            res.status(500).json({ success: false, message: 'حدث خطأ أثناء تحديث الحالة' });
        }
    }

    // API: الحصول على جميع الفئات النشطة
    async getActiveCategories(req, res) {
        try {
            const categories = await Category.findAll({
                where: { status: 'active' },
                attributes: ['id', 'name', 'description'],
                order: [['name', 'ASC']]
            });

            res.json({
                success: true,
                data: categories
            });
        } catch (error) {
            console.error('Error fetching active categories:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ أثناء جلب الفئات'
            });
        }
    }
}

module.exports = new CategoryController();
