<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل المنتج - نظام إدارة المتجر</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #B2CD9C;
            --secondary-color: #8FBC8F;
            --accent-color: #7BA05B;
            --text-dark: #2c3e50;
            --text-light: #7f8c8d;
            --bg-light: #f8f9fa;
            --white: #ffffff;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--bg-light) 0%, #e8f5e8 100%);
            color: var(--text-dark);
            line-height: 1.6;
        }

        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: var(--shadow);
            padding: 1rem 0;
        }

        .navbar-brand {
            color: var(--white) !important;
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem !important;
            border-radius: 8px;
            margin: 0 0.2rem;
        }

        .nav-link:hover {
            color: var(--white) !important;
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white) !important;
        }

        .container-main {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .form-card {
            background: var(--white);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--shadow);
            border-left: 5px solid var(--primary-color);
        }

        .form-label {
            color: var(--text-dark);
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(178, 205, 156, 0.25);
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: var(--white);
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow);
            color: var(--white);
        }

        .btn-secondary-custom {
            background: #6c757d;
            border: none;
            color: var(--white);
            padding: 0.75rem 2rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-secondary-custom:hover {
            background: #5a6268;
            transform: translateY(-2px);
            color: var(--white);
        }

        .file-upload-area {
            border: 2px dashed var(--primary-color);
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            background: rgba(178, 205, 156, 0.1);
            transition: all 0.3s ease;
        }

        .file-upload-area:hover {
            background: rgba(178, 205, 156, 0.2);
        }

        .current-images {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .current-image {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: var(--shadow);
        }

        .current-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .delete-image {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .alert-custom {
            border: none;
            border-radius: 10px;
            padding: 1rem 1.5rem;
        }

        .alert-danger {
            background: rgba(231, 76, 60, 0.1);
            color: #c0392b;
            border-left: 4px solid #e74c3c;
        }

        @media (max-width: 768px) {
            .container-main {
                padding: 1rem;
            }
            
            .form-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom">
        <div class="container">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-store me-2"></i>
                نظام إدارة المتجر
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/customers">
                            <i class="fas fa-users me-1"></i>العملاء
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/admin/products">
                            <i class="fas fa-box me-1"></i>المنتجات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/categories">
                            <i class="fas fa-tags me-1"></i>الفئات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/orders">
                            <i class="fas fa-shopping-cart me-1"></i>الطلبات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/admin/deliveries">
                            <i class="fas fa-truck me-1"></i>التوصيلات
                        </a>
                    </li>
                </ul>
                
                <div class="dropdown">
                    <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user-circle me-1"></i>
                        المدير
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/admin/profile">
                            <i class="fas fa-user me-2"></i>الملف الشخصي
                        </a></li>
                        <li><a class="dropdown-item" href="/admin/settings">
                            <i class="fas fa-cog me-2"></i>الإعدادات
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="/admin/auth/logout">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-main">
        <div class="form-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 style="color: var(--primary-color);">
                        <i class="fas fa-edit me-2"></i>
                        تعديل المنتج
                    </h2>
                    <p class="text-muted mb-0">تعديل بيانات المنتج: <%= product.name %></p>
                </div>
                <div class="d-flex gap-2">
                    <a href="/admin/products/<%= product.id %>" class="btn btn-secondary-custom">
                        <i class="fas fa-eye me-2"></i>عرض
                    </a>
                    <a href="/admin/products" class="btn btn-secondary-custom">
                        <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
                    </a>
                </div>
            </div>

            <% if (typeof error !== 'undefined' && error) { %>
                <div class="alert alert-danger alert-custom mb-4">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <%= error %>
                </div>
            <% } %>

            <form action="/admin/products/<%= product.id %>" method="POST" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-tag me-2"></i>اسم المنتج *
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="<%= product.name %>" 
                                   required placeholder="أدخل اسم المنتج">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">
                                <i class="fas fa-align-left me-2"></i>وصف المنتج
                            </label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="أدخل وصف المنتج"><%= product.description || '' %></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">
                                        <i class="fas fa-money-bill me-2"></i>السعر (ر.س) *
                                    </label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           value="<%= product.price %>" 
                                           step="0.01" min="0" required placeholder="0.00">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">
                                        <i class="fas fa-boxes me-2"></i>الكمية المتاحة *
                                    </label>
                                    <input type="number" class="form-control" id="quantity" name="quantity" 
                                           value="<%= product.quantity %>" 
                                           min="0" required placeholder="0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="categoryId" class="form-label">
                                <i class="fas fa-list me-2"></i>الفئة *
                            </label>
                            <select class="form-select" id="categoryId" name="categoryId" required>
                                <option value="">اختر الفئة</option>
                                <% if (typeof categories !== 'undefined' && categories) { %>
                                    <% categories.forEach(category => { %>
                                        <option value="<%= category.id %>" 
                                                <%= product.categoryId == category.id ? 'selected' : '' %>>
                                            <%= category.name %>
                                        </option>
                                    <% }); %>
                                <% } %>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-images me-2"></i>الصور الحالية
                            </label>
                            <% if (product.images && product.images.length > 0) { %>
                                <div class="current-images">
                                    <% product.images.forEach(image => { %>
                                        <div class="current-image">
                                            <img src="<%= image.image %>" alt="<%= product.name %>">
                                            <button type="button" class="delete-image" 
                                                    onclick="deleteImage(<%= image.id %>)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    <% }); %>
                                </div>
                            <% } else { %>
                                <p class="text-muted">لا توجد صور</p>
                            <% } %>
                        </div>

                        <div class="mb-3">
                            <label for="images" class="form-label">
                                <i class="fas fa-plus me-2"></i>إضافة صور جديدة
                            </label>
                            <div class="file-upload-area">
                                <i class="fas fa-cloud-upload-alt fa-2x mb-2" style="color: var(--primary-color);"></i>
                                <p class="mb-2">اسحب الصور هنا أو انقر للاختيار</p>
                                <input type="file" class="form-control" id="images" name="images" 
                                       multiple accept="image/*" style="display: none;">
                                <button type="button" class="btn btn-outline-primary btn-sm" 
                                        onclick="document.getElementById('images').click()">
                                    <i class="fas fa-folder-open me-1"></i>اختيار الصور
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end gap-3 mt-4">
                    <a href="/admin/products" class="btn btn-secondary-custom">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                    <button type="submit" class="btn btn-custom">
                        <i class="fas fa-save me-2"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // File upload preview
        document.getElementById('images').addEventListener('change', function(e) {
            const files = e.target.files;
            if (files.length > 0) {
                const uploadArea = document.querySelector('.file-upload-area');
                uploadArea.innerHTML = `
                    <i class="fas fa-check-circle fa-2x mb-2" style="color: var(--primary-color);"></i>
                    <p class="mb-0">تم اختيار ${files.length} صورة جديدة</p>
                `;
            }
        });

        // Delete image function
        function deleteImage(imageId) {
            if (confirm('هل أنت متأكد من حذف هذه الصورة؟')) {
                fetch(`/admin/products/images/${imageId}/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء حذف الصورة');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء حذف الصورة');
                });
            }
        }
    </script>
</body>
</html>
