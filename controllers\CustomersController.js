const BaseController = require('./BaseController');
const { Customer, Area, Order, Country, Category, Store, CustomerArea, Product, Cart, Image, OrderDetail, Notification, sequelize } = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { Op, and } = require('sequelize');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const CartService = require('../services/CartService');
const { Console } = require('winston/lib/winston/transports');

class CustomersController extends BaseController {
    constructor() {
        super(Customer, 'customers');
    }

    /**
     * API: الحصول على الصفحة الرئيسية للعميل مع الفئات والمتاجر
     * GET /api/customers/home
     */
    async getHome(req, res) {
    try {
        const customerId = req.customer?.id;

        // المنتجات التي تحتوي على صورة (offers)
        const offerProducts = await Product.findAll({
        where: {
            image: {
            [Op.and]: [
                { [Op.ne]: null },
                { [Op.ne]: '' }
            ]
            },
            status: 'active'
        },
        include: [
            {
            model: Image,
            as: 'images',
            attributes: ['image'],
            required: false
            }
        ],
        attributes: ['id', 'name', 'description', 'price', 'quantity', 'image']
        });

        // المنتجات الأعلى تقييماً
        const topProducts = await Product.findAll({
        where: {
            rating: {
            [Op.not]: null
            },
            status: 'active'
        },
        include: [
            {
            model: Image,
            as: 'images',
            attributes: ['image'],
            required: false
            }
        ],
        attributes: ['id', 'name', 'description', 'price', 'quantity', 'rating'],
        order: [['rating', 'DESC']],
        limit: 10
        });

        // الاستجابة
        res.json({
        success: true,
        message: 'تم جلب البيانات بنجاح',
        data: {
            offerProducts,
            topProducts
        }
        });

    } catch (error) {
        console.error('خطأ في جلب الصفحة الرئيسية:', error);
        res.status(500).json({
        success: false,
        message: 'حدث خطأ في الخادم',
        data: null
        });
    }
    }

   

     async getCategory(req, res) {
        try {
            const customerId = req.customer?.id;
            // جلب الفئات مع المتاجر
            const categories = await Category.findAll({
              attributes: ['id', 'name'],
                order: [['name', 'ASC']]
            });
            
            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data:  categories
            });

        } catch (error) {
            console.error('خطأ في جلب الصفحة الرئيسية:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    async showall(req, res) {
      try {
          // إعداد خيارات البحث والفلتر
          const searchFields = {
              text: ['name', 'barcode', 'phoneNumber'],
              numeric: ['id']
          };

          const filterFields = {
              status: { type: 'exact' },
              createdAt: { type: 'date' }
          };

          // بناء شروط البحث والفلتر
          const whereClause = buildSearchAndFilter(req.query, searchFields, filterFields);

          // خيارات الترتيب
          const sortOptions = buildSortOptions(req.query.sortBy, req.query.sortOrder, 'createdAt');

          // خيارات الـ pagination
          const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit);

          // جلب البيانات
          const { count, rows: customers } = await Customer.findAndCountAll({
              where: whereClause,
              include: [
                  { model: Area, as: 'areas', through: 'CustomerAreas' },
                  { model: Order, as: 'orders' }
              ],
              order: sortOptions,
              limit: paginationOptions.limit,
              offset: paginationOptions.offset,
              distinct: true
          });

          // حساب معلومات الـ pagination
          const pagination = calculatePaginationInfo(count, paginationOptions.page, paginationOptions.limit);

          // تنظيف الفلاتر للعرض
          const filters = sanitizeFilters(req.query);

          // خيارات الترتيب للعرض
          const sortOptionsForView = [
              { value: 'createdAt', label: 'تاريخ الإنشاء' },
              { value: 'name', label: 'اسم العميل' },
              { value: 'barcode', label: 'الباركود' },
              { value: 'status', label: 'الحالة' }
          ];

          res.render('admin/customers/index', {
              customers,
              pagination,
              filters,
              sortOptions: sortOptionsForView,
              currentUrl: req.originalUrl,
              originalUrl: req.originalUrl,
              activeFiltersCount: Object.keys(filters).length
          });
      } catch (error) {
          console.error('Error fetching customers:', error);
          res.status(500).render('error', { error });
      }
  }

    async show(req, res) {
        const customerId = req.params.id;

        try {
            const customer = await Customer.findByPk(customerId, {
                include: [{
                  model: Area,
                  as: 'areas',
                  include: [{ model: Country, as: 'country' }]
                }]
              });

          if (!customer) {
            return res.status(404).render('error', { error: { message: 'الزبون غير موجود' } });
          }

          res.render('stores/customers', { customer });
        } catch (error) {
          console.error("Error fetching customer:", error);
          res.status(500).render('error', { error: { message: 'حدث خطأ أثناء جلب بيانات الزبون' } });
        }
      }


    /**
     * API: الحصول على متاجر حسب الفئة
     * GET /api/customers/categories/:id
     */
    async getStoresByCategory(req, res) {
        try {
            const categoryId = req.params.id;
            const { page = 1, limit = 10, search = '', areaId = null } = req.query;
            console.log("getStoresByCategory");
            const category = await Category.findByPk(categoryId);
            if (!category) {
                return res.status(404).json({
                    success: false,
                    message: 'الفئة غير موجودة',
                    data: null
                });
            }

            const offset = (page - 1) * limit;
            const whereClause = { status: "active" };

            // فلتر البحث
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }

            // فلتر المنطقة
            if (areaId) {
                whereClause.areaId = areaId;
            }

            // بناء include بشكل مرن
            const includeClause = [
                {
                    model: Area,
                    as: 'area',
                    attributes: ['name']
                }
            ];

            // تنفيذ الاستعلام
            const { count, rows: stores } = await Store.findAndCountAll({
                where: whereClause,
                include: includeClause,
                attributes: ['id', 'name', 'address', 'phoneNumber'],
                limit: parseInt(limit) || 10,
                offset: parseInt(offset) || 0,
                order: [['createdAt', 'DESC']],
                distinct: true
            });



            res.json({
                success: true,
                message: 'تم جلب المتاجر بنجاح',
                data: {
                   /* category: {
                        id: category.id,
                        name: category.name,
                        description: category.description
                    },*/
                    stores,
                  /*  pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب متاجر الفئة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: الحصول على منتجات متجر معين
     * GET /api/customers/stores/:id
     */
    async getStoreProducts(req, res) {
        try {
            console.log("getStoreProducts");
            const storeId = parseInt(req.params.id);
            const { page = 1, limit = 10, search = '', categoryId = null, minPrice = null, maxPrice = null } = req.query;

            const store = await Store.findByPk(storeId, {
                where: { status: "active" },
                attributes: ['id', 'name', 'address', 'phoneNumber']
            });

            if (!store) {
                return res.status(404).json({
                    success: false,
                    message: 'المتجر غير موجود',
                    data: null
                });
            }

            const offset = (page - 1) * limit;
            const whereClause = {
                storeId: storeId,
                status: "active"
            };

            // فلتر البحث
            if (search) {
                whereClause[Op.or] = [
                    { name: { [Op.like]: `%${search}%` } },
                    { description: { [Op.like]: `%${search}%` } }
                ];
            }

            // فلتر الفئة
            if (categoryId) {
                whereClause.categoryId = categoryId;
            }

            // فلتر السعر
            if (minPrice || maxPrice) {
                whereClause.price = {};
                if (minPrice) whereClause.price[Op.gte] = parseFloat(minPrice);
                if (maxPrice) whereClause.price[Op.lte] = parseFloat(maxPrice);
            }

            const { count, rows: products } = await Product.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image'],
                        required: false
                    }
                ],
                attributes: ['id', 'name', 'description', 'price', 'quantity','createdAt'],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });


            res.json({
                success: true,
                message: 'تم جلب منتجات المتجر بنجاح',
                data: {
                   /* store: {
                        id: store.id,
                        name: store.name,
                        description: store.description,
                        image: store.image ? `/uploads/${store.image}` : null,
                        rating: store.rating || 0,
                        phone: store.phone,
                        address: store.address
                    },*/
                    products: products,
                 /*  pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }*/
                }
            });

        } catch (error) {
            console.error('خطأ في جلب منتجات المتجر:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: الحصول على تفاصيل منتج
     * GET /api/customers/products/:id
     */
    async getProductDetails(req, res) {
        try {
            const productId = req.params.id;

            const product = await Product.findOne({
                where: {
                    id: productId,
                   status: "active"
                },
                attributes: ['id', 'name', 'description', 'price', 'quantity'],
                include: [
                    {
                        model: Store,
                        as: 'store',
                        attributes: ['name'],
                        include: [
                            {
                                model: Area,
                                as: 'area',
                                attributes: ['name']
                            }
                        ]
                    },
                    {
                        model: Category,
                        as: 'category',
                        attributes: ['name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image']
                    }
                ]
            });

            if (!product) {
                return res.status(404).json({
                    success: false,
                    message: 'المنتج غير موجود',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم جلب تفاصيل المنتج بنجاح',
                data: product
            });

        } catch (error) {
            console.error('خطأ في جلب تفاصيل المنتج:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: إضافة منتج للسلة
     * POST /api/customers/cart/add
     */
    async addToCart(req, res) {
        try {
            const customerId = req.customer.id;
            console.log(customerId);
            const { productId, quantity = 1 ,sessionId = null} = req.body;

            const cartItem = await CartService.addToCart(customerId, productId, quantity,sessionId);

            const cartCount = await CartService.getCartCount(customerId, sessionId);

            res.json({
                success: true,
                message: 'تم إضافة المنتج للسلة بنجاح',
                data: {
                    cartItem: cartItem,
                    cartCount: cartCount || 0
                }
            });

        } catch (error) {
            console.error('خطأ في إضافة المنتج للسلة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }


    /**
     * API: عرض محتويات السلة
     * GET /api/customers/cart
     */
    async getCart(req, res) {
        try {
            const customerId = req.customer.id;

            // استخدام CartService
            const cartData = await CartService.getCart(customerId);

            // تنسيق البيانات للإرجاع
            const formattedCartItems = cartData.cartItems.map(item => ({
                id: item.id,
                quantity: item.quantity,
                price: parseFloat(item.price),
                totalPrice: parseFloat(item.totalPrice),
                notes: item.notes,
                product: {
                    id: item.product.id,
                    name: item.product.name,
                    description: item.product.description,
                    price: parseFloat(item.product.price),
                    quantity: item.product.quantity,
                    category: item.product.category ? {
                        id: item.product.category.id,
                        name: item.product.category.name
                    } : null
                },
                store: {
                    name: item.product.store.name,
                }
            }));

            const formattedStoreGroups = cartData.storeGroups.map(group => ({
                store: {
                    name: group.store.name,
                },
                items: group.items.map(item => ({
                    id: item.id,
                    quantity: item.quantity,
                    price: parseFloat(item.price),
                    totalPrice: parseFloat(item.totalPrice),
                    notes: item.notes,
                    product: {
                        id: item.product.id,
                        name: item.product.name,
                        description: item.product.description,
                        price: parseFloat(item.product.price),
                        quantity: item.product.quantity,
                        category: item.product.category ? {
                            id: item.product.category.id,
                            name: item.product.category.name
                        } : null
                    }
                })),
                subtotal: group.subtotal,
                itemsCount: group.itemsCount
            }));

            res.json({
                success: true,
                message: 'تم جلب محتويات السلة بنجاح',
                data: {
                    cartItems: formattedCartItems,
                    storeGroups: formattedStoreGroups,
                    summary: cartData.summary
                }
            });

        } catch (error) {
            console.error('خطأ في جلب السلة:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: حذف منتج من السلة
     * DELETE /api/customers/cart/:id
     */
    async removeFromCart(req, res) {
        try {
            const customerId = req.customer.id;
            const cartItemId = req.params.id;

            // استخدام CartService
            await CartService.removeFromCart(cartItemId, customerId);

            // جلب عدد العناصر المتبقية في السلة
            const cartCount = await CartService.getCartCount(customerId);

            res.json({
                success: true,
                message: 'تم حذف المنتج من السلة بنجاح',
                data: {
                    cartCount: cartCount
                }
            });

        } catch (error) {
            console.error('خطأ في حذف المنتج من السلة:', error);

            let statusCode = 500;
            if (error.message.includes('غير موجود')) {
                statusCode = 404;
            }

            res.status(statusCode).json({
                success: false,
                message: error.message,
                data: null
            });
        }
    }

    /**
     * API: تحديث كمية منتج في السلة
     * PUT /api/customers/cart/:id
     */
    async updateCartItem(req, res) {
        try {
            const customerId = 1;//req.customer.id;
            const cartItemId = req.params.id;
            const { quantity } = req.body;

            if (!quantity || quantity < 1) {
                return res.status(400).json({
                    success: false,
                    message: 'الكمية يجب أن تكون أكبر من صفر',
                    data: null
                });
            }

            // استخدام CartService
            const updatedCartItem = await CartService.updateCartItem(cartItemId, quantity, customerId);

            res.json({
                success: true,
                message: 'تم تحديث الكمية بنجاح',
                data: {
                    cartItem: {
                        id: updatedCartItem.id,
                        quantity: updatedCartItem.quantity,
                        price: parseFloat(updatedCartItem.price),
                        totalPrice: parseFloat(updatedCartItem.totalPrice)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تحديث السلة:', error);

            let statusCode = 500;
            if (error.message.includes('غير موجود')) {
                statusCode = 404;
            } else if (error.message.includes('الكمية') || error.message.includes('متاحة')) {
                statusCode = 400;
            }

            res.status(statusCode).json({
                success: false,
                message: error.message,
                data: null
            });
        }
    }

    /**
     * API: مسح السلة بالكامل
     * DELETE /api/customers/cart
     */
    async clearCart(req, res) {
        try {
            const customerId = req.customer.id;

            // استخدام CartService
            await CartService.clearCart(customerId);

            res.json({
                success: true,
                message: 'تم مسح السلة بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في مسح السلة:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: عرض بيانات الدفع
     * GET /api/mobile/checkout
     */
    async getCheckoutData(req, res) {
        try {
            const customerId = req.customer.id;

            // جلب محتويات السلة باستخدام CartService
            const cartData = await CartService.getCart(customerId);

            if (!cartData.cartItems || cartData.cartItems.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'سلة التسوق فارغة',
                    data: null
                });
            }

            // جلب بيانات العميل
            const customer = await Customer.findByPk(customerId, {
                include: [
                    {
                        model: Area,
                        as: 'areas',
                        include: [
                            {
                                model: Country,
                                as: 'country',
                                attributes: ['id', 'name']
                            }
                        ]
                    }
                ],
                attributes: { exclude: ['password'] }
            });

            // تنسيق بيانات الدفع
            const checkoutData = {
                customer: {
                    id: customer.id,
                    name: customer.name,
                    email: customer.email,
                    phone: customer.phone,
                    address: customer.address,
                    areas: customer.areas ? customer.areas.map(area => ({
                        id: area.id,
                        name: area.name,
                        address: area.CustomerArea ? area.CustomerArea.address : null,
                        country: area.Country ? area.Country.name : null
                    })) : []
                },
                cart: {
                    items: cartData.cartItems.map(item => ({
                        id: item.id,
                        quantity: item.quantity,
                        price: parseFloat(item.price),
                        totalPrice: parseFloat(item.totalPrice),
                        notes: item.notes,
                        product: {
                            id: item.product.id,
                            name: item.product.name,
                            description: item.product.description,
                            availableQuantity: item.product.quantity
                        }
                    })),
                    storeGroups: cartData.storeGroups.map(group => ({
                        store: {
                            id: group.store.id,
                            name: group.store.name,
                            phone: group.store.phone,
                            address: group.store.address,
                        },
                        items: group.items.map(item => ({
                            id: item.id,
                            quantity: item.quantity,
                            price: parseFloat(item.price),
                            totalPrice: parseFloat(item.totalPrice),
                            product: {
                                id: item.product.id,
                                name: item.product.name,
                            }
                        })),
                        subtotal: group.subtotal,
                        itemsCount: group.itemsCount
                    })),
                    summary: cartData.summary
                }
            };

            res.json({
                success: true,
                message: 'تم جلب بيانات الدفع بنجاح',
                data: checkoutData
            });

        } catch (error) {
            console.error('خطأ في جلب بيانات الدفع:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * API: معالجة إنشاء الطلب
     * POST /api/mobile/checkout
     */
    async processCheckout(req, res) {
        const transaction = await sequelize.transaction();

        try {
            const customerId = req.customer.id;
            console.log(req.body);
            const { deliveryAddress, notes } = req.body;

            // التحقق من البيانات المطلوبة
            if (!deliveryAddress) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: 'عنوان التوصيل مطلوب',
                    data: null
                });
            }
            console.log('customerId:', customerId);
            // جلب محتويات السلة
            const cartData = await CartService.getCart(customerId);
            console.log('cartData:', JSON.stringify(cartData, null, 2));

            if (!cartData.cartItems || cartData.cartItems.length === 0) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: 'سلة التسوق فارغة',
                    data: null
                });
            }

            // التحقق من توفر جميع المنتجات
            for (const item of cartData.cartItems) {
                if (item.product.quantity < item.quantity) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: `المنتج "${item.product.name}" غير متوفر بالكمية المطلوبة. المتاح: ${item.product.quantity}`,
                        data: {
                            productId: item.product.id,
                            availableQuantity: item.product.quantity,
                            requestedQuantity: item.quantity
                        }
                    });
                }
            }
            // إنشاء طلب منفصل لكل متجر
            const createdOrders = [];

            for (const storeGroup of cartData.storeGroups) {
                console.log('storeGroup:', JSON.stringify(storeGroup, null, 2));

                // التحقق من وجود بيانات المتجر
                if (!storeGroup.store || !storeGroup.store.id) {
                    await transaction.rollback();
                    return res.status(400).json({
                        success: false,
                        message: 'خطأ في بيانات المتجر',
                        data: { storeGroup }
                    });
                }

                const orderData = {
                    customerId: customerId,
                    storeId: storeGroup.store.id,
                    totalPrice: storeGroup.subtotal,
                    status: 'pending',
                    deliveryAddress: deliveryAddress,
                    notes: notes || null
                };

                console.log('orderData:', orderData);

                // إنشاء الطلب
                const order = await Order.create(orderData, { transaction });

                // إنشاء تفاصيل الطلب
                const orderDetails = storeGroup.items.map(item => ({
                    orderId: order.id,
                    productId: item.product.id,
                    quantity: item.quantity,
                    totalPrice: parseFloat(item.totalPrice)
                }));

                await OrderDetail.bulkCreate(orderDetails, { transaction });
                /*
                // تحديث كمية المنتجات
                for (const item of storeGroup.items) {
                    await Product.update(
                        { quantity: item.product.quantity - item.quantity },
                        { where: { id: item.product.id } },
                        { transaction }
                    );
                }*/

                    // إنشاء إشعار للمتجر
                await Notification.create({
                    type: 'order',
                    title: 'طلب جديد',
                    message: `تم استلام طلب جديد رقم #${order.id} من العميل`,
                    targetType: 'store',
                    targetId: storeGroup.store.id,
                    relatedId: order.id,
                    isRead: false
                }, { transaction });

                createdOrders.push({
                    orderId: order.id,
                    storeId: storeGroup.store.id,
                    storeName: storeGroup.store.name,
                    subtotal: storeGroup.subtotal,
                    itemsCount: storeGroup.itemsCount
                });
            }

            // إنشاء إشعار للعميل
            await Notification.create({
                type: 'order',
                title: 'تأكيد الطلب',
                message: `تم إنشاء ${createdOrders.length} طلب بنجاح بإجمالي ${cartData.summary.totalPrice} ريال`,
                targetType: 'customer',
                targetId: customerId,
                relatedId: createdOrders[0].orderId,
                isRead: false
            }, { transaction });

            // مسح السلة
            await CartService.clearCart(customerId);

            await transaction.commit();

            res.status(201).json({
                success: true,
                message: `تم إنشاء ${createdOrders.length} طلب بنجاح`,
                data: {
                    orders: createdOrders,
                    summary: {
                        totalOrders: createdOrders.length,
                        totalAmount: cartData.summary.totalPrice,
                        totalItems: cartData.summary.totalItems,
                        deliveryAddress: deliveryAddress
                    }
                }
            });

        } catch (error) {
            await transaction.rollback();
            console.error('خطأ في معالجة الطلب:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في معالجة الطلب',
                data: null
            });
        }
    }
    

    /**
     * API: معالجة إنشاء الطلب
     * POST /checkout
     */
  async processAddCheckout(req, res) {
    const transaction = await sequelize.transaction();
    console.log(req.body);
    try {
        const customerId = req.customer.id;
        const { deliveryAddress, items } = req.body;
        console.log(deliveryAddress);
        console.log(items);
        if (!deliveryAddress || !Array.isArray(items) || items.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'عنوان التوصيل والمشتريات مطلوبة',
                data: null
            });
        }

        let totalPrice = 0;
        const orderDetails = [];

        for (const item of items) {
            // تحقق من وجود المنتج فعلياً في قاعدة البيانات
            const product = await Product.findByPk(item.id);

            if (!product) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: `المنتج بالمعرف ${item.id} غير موجود`,
                    data: { productId: item.id }
                });
            }

            // تحقق من توفر الكمية
            if (product.quantity < item.quntity) {
                await transaction.rollback();
                return res.status(400).json({
                    success: false,
                    message: `الكمية غير متوفرة للمنتج ${product.name}`,
                    data: {
                        productId: product.id,
                        available: product.quantity,
                        requested: item.quntity
                    }
                });
            }

            const itemTotal = parseFloat(item.Price) * parseInt(item.quntity);
            totalPrice += itemTotal;

            orderDetails.push({
                productId: product.id,
                quantity: item.quntity,
                totalPrice: itemTotal
            });
/*
            // يمكنك إنقاص الكمية إذا رغبت
            await Product.update(
                { quantity: product.quantity - item.quntity },
                { where: { id: product.id }, transaction }
            );*/
        }

        // إنشاء الطلب الرئيسي
        const order = await Order.create({
            customerId,
            storeId: null, // إذا ما كان عندك تحديد للمتجر
            totalPrice,
            status: 'pending',
            deliveryAddress,
            notes: notes || null
        }, { transaction });

        // ربط التفاصيل بالطلب
        const fullDetails = orderDetails.map(detail => ({
            ...detail,
            orderId: order.id
        }));

        await OrderDetail.bulkCreate(fullDetails, { transaction });

        // إشعار العميل
        await Notification.create({
            type: 'order',
            title: 'تم استلام الطلب',
            message: `تم إنشاء طلبك رقم #${order.id} بنجاح.`,
            targetType: 'customer',
            targetId: customerId,
            relatedId: order.id,
            isRead: false
        }, { transaction });

        await transaction.commit();

        return res.status(201).json({
            success: true,
            message: 'تم إنشاء الطلب بنجاح',
            data: {
                orderId: order.id,
                totalAmount: totalPrice,
                totalItems: orderDetails.length
            }
        });

    } catch (error) {
        await transaction.rollback();
        console.error('خطأ في معالجة الطلب:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'حدث خطأ أثناء تنفيذ الطلب',
            data: null
        });
    }
}



    // إرسال إشعار للمتجر عن الطلب الجديد
    async sendNewOrderNotification(order, storeGroup) {
    try {
        const NotificationService = require('../services/NotificationService');

        const itemsList = storeGroup.items.map(item =>
            `${item.product.name} (${item.quantity} × ${item.price} ريال)`
        ).join('\n');

        await NotificationService.notifyStore(order.storeId, {
            title: 'طلب جديد يحتاج للمعالجة',
            message: `لديك طلب جديد رقم #${order.id}\n\nالمنتجات:\n${itemsList}\n\nإجمالي المبلغ: ${storeGroup.subtotal} ريال\n\nعنوان التوصيل: ${order.deliveryAddress}`,
            type: 'order',
            priority: 'high',
            actionUrl: `/store/orders/${order.id}`,
            actionText: 'معالجة الطلب',
            data: {
                orderId: order.id,
                customerId: order.customerId,
                totalAmount: storeGroup.subtotal,
                itemsCount: storeGroup.items.length,
                type: 'new_order'
            }
        });

        console.log(`Order notification sent to store ${order.storeId} for order ${order.id}`);

    } catch (error) {
        console.error('Error in sendNewOrderNotification:', error);
        throw error;
    }
    }

    // إرسال إشعار تأكيد للعميل
    async sendOrderConfirmationNotification(customerId, createdOrders, totalAmount) {
    try {
        const NotificationService = require('../services/NotificationService');

        const ordersList = createdOrders.map(({ order, storeGroup }) =>
            `طلب #${order.id} من ${storeGroup.store.name} (${storeGroup.subtotal} ريال)`
        ).join('\n');

        await NotificationService.notifyCustomer(customerId, {
            title: 'تم إنشاء طلباتك بنجاح',
            message: `تم إنشاء طلباتك بنجاح:\n\n${ordersList}\n\nإجمالي المبلغ: ${totalAmount} ريال\n\nسيتم معالجة طلباتك من قبل المتاجر قريباً.`,
            type: 'success',
            priority: 'normal',
            actionUrl: '/customers/orders',
            actionText: 'عرض طلباتي',
            data: {
                ordersCount: createdOrders.length,
                totalAmount: totalAmount,
                type: 'orders_created'
            }
        });

        console.log(`Order confirmation notification sent to customer ${customerId}`);

    } catch (error) {
        console.error('Error in sendOrderConfirmationNotification:', error);
        throw error;
    }
    }


    /**
     * API: الحصول على طلبات العميل
     * GET /api/mobile/orders
     */
    async getOrders(req, res) {
        try {
            const customerId = req.customer.id;
            const {
                page = 1,
                limit = 10,
                status = null,
                storeId = null,
                startDate = null,
                endDate = null
            } = req.query;

            const offset = (page - 1) * limit;
            const whereClause = { customerId: customerId };

            // فلتر بالحالة
            if (status) {
                whereClause.status = status;
            }

            // فلتر بالمتجر
            if (storeId) {
                whereClause.storeId = storeId;
            }

            // فلتر بالتاريخ
            if (startDate || endDate) {
                whereClause.createdAt = {};
                if (startDate) whereClause.createdAt[Op.gte] = new Date(startDate);
                if (endDate) whereClause.createdAt[Op.lte] = new Date(endDate);
            }

            const { count, rows: orders } = await Order.findAndCountAll({
                where: whereClause,
                include: [
                    {
                        model: Store,
                        as: 'store',
                        attributes: ['name']
                    },
                    {
                        model: OrderDetail,
                        as: 'orderDetails',
                        include: [
                            {
                                model: Product,
                                as: 'product',
                                attributes: ['id', 'name', 'price'],
                                include: [
                                    {
                                        model: Category,
                                        as: 'category',
                                        attributes: ['id', 'name']
                                    }
                                ]
                            }
                        ]
                    }
                ],
                limit: parseInt(limit),
                offset: parseInt(offset),
                order: [['createdAt', 'DESC']]
            });

            // تنسيق البيانات
            const formattedOrders = orders.map(order => ({
                id: order.id,
                totalPrice: parseFloat(order.totalPrice),
                status: order.status,
                Address: order.deliveryAddress,
                notes: order.notes,
                createdAt: order.createdAt,
                updatedAt: order.updatedAt,
                store: order.Store ? {
                    name: order.Store.name,
                } : null,
                items: order.orderDetails ? order.orderDetails.map(detail => ({
                    id: detail.id,
                    quantity: detail.quantity,
                    price: parseFloat(detail.price),
                    subtotal: parseFloat(detail.subtotal),
                    product: detail.product ? {
                        id: detail.product.id,
                        name: detail.product.name,
                        //image: detail.product.image ? `/uploads/${detail.product.image}` : null,
                        category: detail.product.Category ? detail.product.Category.name : null
                    } : null
                })) : [],
                itemsCount: order.orderDetails ? order.orderDetails.length : 0
            }));

            res.json({
                success: true,
                message: 'تم جلب الطلبات بنجاح',
                data: {
                    orders: formattedOrders,
                    pagination: {
                        currentPage: parseInt(page),
                        totalPages: Math.ceil(count / limit),
                        totalItems: count,
                        itemsPerPage: parseInt(limit)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في جلب الطلبات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    /**
     * الحصول على نص الحالة بالعربية
     */
    getStatusText(status) {
        const statusTexts = {
            'pending': 'في الانتظار',
            'accepted': 'مقبول',
            'preparing': 'قيد التحضير',
            'ready': 'جاهز للتسليم',
            'delivered': 'تم التسليم',
            'completed': 'مكتمل',
            'cancelled': 'ملغي',
            'rejected': 'مرفوض'
        };
        return statusTexts[status] || status;
    }

    // عرض صفحة البروفايل
    async profile(req, res) {
    try {
        const customerId = 1;//req.customer.id;

        const customer = await Customer.findByPk(customerId, {
            include: {
                model: Area,
                as: 'areas'
            }
        });

        const areas = await Area.findAll({
            attributes: ['id', 'name']
        });

        res.json({
            success: true,
            message: 'تم جلب بيانات الملف الشخصي بنجاح',
            data: {
                customer,
                areas
            }
        });

    } catch (error) {
        console.error('خطأ في جلب الملف الشخصي:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ أثناء تحميل الملف الشخصي',
            data: null
        });
    }
    };


    // تحديث العنوان فقط
    async addAddress(req, res) {
    try {
        const customerId = req.user.id;
        const { areaId, address } = req.body;

        try {
        await CustomerArea.create({ customer_id: customerId, area_id: areaId, address });
        res.redirect('/customers/profile');
        } catch (err) {
        console.error(err);
        res.status(500).send('Error adding address');
        }
    } catch (err) {
        console.error('Address update error:', err);
        res.status(500).send('Error adding address');
    }
    };

    async deleteAddress(req, res) {
    const addressId = req.params.id;
    const customerId = req.user.id;
    try {
        await CustomerArea.destroy({ where: { area_id: addressId , customer_id: customerId } });
        res.redirect('/customers/profile');
    } catch (err) {
        console.error(err);
        res.status(500).send('Error deleting address');
    }
    };

    async create(req, res) {
        try {
            const areas = await Area.findAll();
            res.render('admin/customers/create', { areas });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async store(req, res) {
        try {
            const { areas, ...customerData } = req.body;
            const customer = await Customer.create(customerData);
            if (areas) {
                await customer.setAreas(Array.isArray(areas) ? areas : [areas]);
            }
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async edit(req, res) {
        try {
            const [customer, areas] = await Promise.all([
                Customer.findByPk(req.params.id, {
                    include: [{ model: Area, as: 'areas', through: 'CustomerAreas' }]
                }),
                Area.findAll()
            ]);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            res.render('admin/customers/edit', { customer, areas });
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async update(req, res) {
        try {
            const { areas, ...customerData } = req.body;
            const customer = await Customer.findByPk(req.params.id);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            await customer.update(customerData);
            if (areas) {
                await customer.setAreas(Array.isArray(areas) ? areas : [areas]);
            }
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    // دالة الحذف
    async delete(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);
            if (!customer) {
                return res.status(404).render('error', { error: 'Customer not found' });
            }
            await customer.destroy();
            req.flash('success', 'Customer deleted successfully');
            res.redirect('/admin/customers');
        } catch (error) {
            res.status(500).render('error', { error });
        }
    }

    async updateStatus(req, res) {
        try {
            const { id } = req.params;
            const { status } = req.body;
            console.log(status);
            if (!['active', 'inactive', 'pending'].includes(status)) {
                req.flash('error', 'Invalid status value');
                return res.redirect('/admin/customers');
            }

            const customer = await Customer.findByPk(id);
            if (!customer) {
                return res.status(404).render('error', {
                    error: { status: 404, message: 'Store not found' }
                });
            }

            await Customer.update({ status }, { where: { id } });
            req.flash('success', `Customer status updated to ${status}`);
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer status:', error);
            res.status(500).render('error', { error: { message: 'Unable to update customer status' } });
        }
    }
}

module.exports = new CustomersController();
