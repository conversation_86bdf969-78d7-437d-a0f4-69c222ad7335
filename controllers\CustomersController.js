const BaseController = require('./BaseController');
const { Customer, Order, Category, CustomerArea, Product, Image, OrderDetail, Notification, sequelize } = require('../models');
const { buildSearchAndFilter, buildSortOptions, buildPaginationOptions, calculatePaginationInfo, sanitizeFilters } = require('../utils/searchFilter');
const { Op } = require('sequelize');
const bcrypt = require('bcrypt');

class CustomersController extends BaseController {
    constructor() {
        super(Customer, 'customers');
    }

    // عرض قائمة العملاء للإدارة
    async index(req, res) {
        try {
            // إعداد خيارات البحث والفلتر
            const searchFields = {
                text: ['name', 'barcode', 'phoneNumber'],
                numeric: ['id']
            };

            const filterFields = {
                status: { type: 'exact' },
                createdAt: { type: 'date' }
            };

            // بناء شروط البحث والفلتر
            const whereClause = buildSearchAndFilter(req.query, searchFields, filterFields);

            // خيارات الترتيب
            const sortOptions = buildSortOptions(req.query.sortBy, req.query.sortOrder, 'createdAt');

            // خيارات الـ pagination
            const paginationOptions = buildPaginationOptions(req.query.page, req.query.limit);

            // جلب البيانات
            const { count, rows: customers } = await Customer.findAndCountAll({
                where: whereClause,
                include: [
                    { model: Order, as: 'orders' }
                ],
                order: sortOptions,
                limit: paginationOptions.limit,
                offset: paginationOptions.offset,
                distinct: true
            });

            // حساب معلومات الـ pagination
            const pagination = calculatePaginationInfo(count, paginationOptions.page, paginationOptions.limit);

            // تنظيف الفلاتر للعرض
            const filters = sanitizeFilters(req.query);

            res.render('admin/customers/index', {
                customers,
                pagination,
                filters,
                currentUrl: req.originalUrl,
                originalUrl: req.originalUrl,
                activeFiltersCount: Object.keys(filters).length
            });
        } catch (error) {
            console.error('Error fetching customers:', error);
            res.status(500).render('error', { error: { message: 'Unable to fetch customers' } });
        }
    }

    // عرض تفاصيل عميل
    async show(req, res) {
        const customerId = req.params.id;

        try {
            const customer = await Customer.findByPk(customerId, {
                include: [
                    { model: Order, as: 'orders' }
                ]
            });

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            res.render('admin/customers/show', { customer });
        } catch (error) {
            console.error("Error fetching customer:", error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء جلب بيانات العميل' } });
        }
    }

    // عرض نموذج إضافة عميل جديد
    async create(req, res) {
        try {
            res.render('admin/customers/create', {
                title: 'إضافة عميل جديد'
            });
        } catch (error) {
            console.error('Error loading create customer form:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل النموذج' } });
        }
    }

    // حفظ عميل جديد
    async store(req, res) {
        try {
            const { name, phoneNumber, password, barcode, discountRate, notes, status, address, latitude, longitude, permissions } = req.body;

            // التحقق من البيانات المطلوبة
            if (!name || !phoneNumber || !password) {
                return res.status(400).render('admin/customers/create', {
                    error: 'الاسم ورقم الهاتف وكلمة المرور مطلوبة',
                    formData: req.body
                });
            }

            // التحقق من عدم وجود عميل بنفس رقم الهاتف
            const existingCustomer = await Customer.findOne({ where: { phoneNumber } });
            if (existingCustomer) {
                return res.status(400).render('admin/customers/create', {
                    error: 'يوجد عميل مسجل بهذا رقم الهاتف',
                    formData: req.body
                });
            }

            // تشفير كلمة المرور
            const hashedPassword = await bcrypt.hash(password, 10);

            // معالجة رفع الصورة
            let imagePath = null;
            if (req.file) {
                imagePath = req.file.filename;
            }

            // معالجة الصلاحيات
            let customerPermissions = null;
            if (permissions && Array.isArray(permissions)) {
                customerPermissions = permissions;
            }

            // إنشاء العميل
            await Customer.create({
                name,
                phoneNumber,
                password: hashedPassword,
                barcode: barcode || null,
                image: imagePath,
                discountRate: parseFloat(discountRate) || 0,
                notes: notes || null,
                address: address || null,
                latitude: latitude ? parseFloat(latitude) : null,
                longitude: longitude ? parseFloat(longitude) : null,
                permissions: customerPermissions,
                status: status || 'active'
            });

            req.flash('success', 'تم إضافة العميل بنجاح');
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error creating customer:', error);
            res.status(500).render('admin/customers/create', {
                error: 'حدث خطأ أثناء إضافة العميل',
                formData: req.body
            });
        }
    }

    // عرض نموذج تعديل عميل
    async edit(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            res.render('admin/customers/edit', {
                customer,
                title: `تعديل العميل: ${customer.name}`
            });
        } catch (error) {
            console.error('Error loading edit customer form:', error);
            res.status(500).render('error', { error: { message: 'حدث خطأ أثناء تحميل النموذج' } });
        }
    }

    // تحديث بيانات عميل
    async update(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            const { name, phoneNumber, barcode, discountRate, notes, status, password, address, latitude, longitude, permissions } = req.body;

            // التحقق من عدم وجود عميل آخر بنفس رقم الهاتف
            if (phoneNumber !== customer.phoneNumber) {
                const existingCustomer = await Customer.findOne({
                    where: {
                        phoneNumber,
                        id: { [Op.ne]: customer.id }
                    }
                });

                if (existingCustomer) {
                    return res.status(400).render('admin/customers/edit', {
                        error: 'يوجد عميل آخر مسجل بهذا رقم الهاتف',
                        customer: { ...customer.toJSON(), ...req.body }
                    });
                }
            }

            // معالجة رفع الصورة الجديدة
            let imagePath = customer.image;
            if (req.file) {
                imagePath = req.file.filename;
            }

            // معالجة الصلاحيات
            let customerPermissions = customer.permissions;
            if (permissions && Array.isArray(permissions)) {
                customerPermissions = permissions;
            } else if (!permissions) {
                customerPermissions = [];
            }

            // إعداد البيانات للتحديث
            const updateData = {
                name,
                phoneNumber,
                barcode: barcode || null,
                image: imagePath,
                discountRate: parseFloat(discountRate) || 0,
                notes: notes || null,
                address: address || null,
                latitude: latitude ? parseFloat(latitude) : null,
                longitude: longitude ? parseFloat(longitude) : null,
                permissions: customerPermissions,
                status
            };

            // تحديث كلمة المرور إذا تم إدخالها
            if (password && password.trim() !== '') {
                updateData.password = await bcrypt.hash(password, 10);
            }

            // تحديث البيانات
            await customer.update(updateData);

            req.flash('success', 'تم تحديث بيانات العميل بنجاح');
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer:', error);
            res.status(500).render('admin/customers/edit', {
                error: 'حدث خطأ أثناء تحديث البيانات',
                customer: { ...req.body, id: req.params.id }
            });
        }
    }

    // حذف عميل
    async delete(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            // حذف ناعم - تغيير الحالة إلى inactive
            await customer.update({ status: 'inactive' });

            req.flash('success', 'تم حذف العميل بنجاح');
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error deleting customer:', error);
            req.flash('error', 'حدث خطأ أثناء حذف العميل');
            res.redirect('/admin/customers');
        }
    }

    // تحديث حالة عميل
    async updateStatus(req, res) {
        try {
            const customer = await Customer.findByPk(req.params.id);

            if (!customer) {
                return res.status(404).render('error', { error: { message: 'العميل غير موجود' } });
            }

            const { status } = req.body;

            if (!['active', 'inactive', 'pending'].includes(status)) {
                req.flash('error', 'حالة غير صحيحة');
                return res.redirect('/admin/customers');
            }

            await customer.update({ status });

            req.flash('success', `تم تحديث حالة العميل إلى ${status}`);
            res.redirect('/admin/customers');
        } catch (error) {
            console.error('Error updating customer status:', error);
            req.flash('error', 'حدث خطأ أثناء تحديث الحالة');
            res.redirect('/admin/customers');
        }
    }

    // API: الحصول على الفئات
    async getCategory(req, res) {
        try {
            const categories = await Category.findAll({
                attributes: ['id', 'name'],
                order: [['name', 'ASC']]
            });
            
            res.json({
                success: true,
                message: 'تم جلب البيانات بنجاح',
                data: categories
            });

        } catch (error) {
            console.error('خطأ في جلب الفئات:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    // API: الحصول على تفاصيل منتج
    async getProductDetails(req, res) {
        try {
            const productId = req.params.id;

            const product = await Product.findOne({
                where: {
                    id: productId,
                    status: "active"
                },
                attributes: ['id', 'name', 'description', 'price', 'quantity'],
                include: [
                    {
                        model: Category,
                        as: 'category',
                        attributes: ['name']
                    },
                    {
                        model: Image,
                        as: 'images',
                        attributes: ['image']
                    }
                ]
            });

            if (!product) {
                return res.status(404).json({
                    success: false,
                    message: 'المنتج غير موجود',
                    data: null
                });
            }

            res.json({
                success: true,
                message: 'تم جلب تفاصيل المنتج بنجاح',
                data: product
            });

        } catch (error) {
            console.error('خطأ في جلب تفاصيل المنتج:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    // API: إضافة منتج للسلة
    async addToCart(req, res) {
        try {
            const customerId = req.customer.id;
            const { productId, quantity = 1, sessionId = null } = req.body;

            const cartItem = await CartService.addToCart(customerId, productId, quantity, sessionId);
            const cartCount = await CartService.getCartCount(customerId, sessionId);

            res.json({
                success: true,
                message: 'تم إضافة المنتج للسلة بنجاح',
                data: {
                    cartItem: cartItem,
                    cartCount: cartCount || 0
                }
            });

        } catch (error) {
            console.error('خطأ في إضافة المنتج للسلة:', error);
            res.status(500).json({
                success: false,
                message: 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    // API: عرض محتويات السلة
    async getCart(req, res) {
        try {
            const customerId = req.customer.id;
            const cartData = await CartService.getCart(customerId);

            // تنسيق البيانات للإرجاع
            const formattedCartItems = cartData.cartItems.map(item => ({
                id: item.id,
                quantity: item.quantity,
                price: parseFloat(item.price),
                totalPrice: parseFloat(item.totalPrice),
                notes: item.notes,
                product: {
                    id: item.product.id,
                    name: item.product.name,
                    description: item.product.description,
                    price: parseFloat(item.product.price),
                    quantity: item.product.quantity,
                    category: item.product.category ? {
                        id: item.product.category.id,
                        name: item.product.category.name
                    } : null
                }
            }));

            res.json({
                success: true,
                message: 'تم جلب محتويات السلة بنجاح',
                data: {
                    cartItems: formattedCartItems,
                    summary: cartData.summary
                }
            });

        } catch (error) {
            console.error('خطأ في جلب السلة:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في الخادم',
                data: null
            });
        }
    }

    // API: حذف منتج من السلة
    async removeFromCart(req, res) {
        try {
            const customerId = req.customer.id;
            const cartItemId = req.params.id;

            await CartService.removeFromCart(cartItemId, customerId);
            const cartCount = await CartService.getCartCount(customerId);

            res.json({
                success: true,
                message: 'تم حذف المنتج من السلة بنجاح',
                data: {
                    cartCount: cartCount
                }
            });

        } catch (error) {
            console.error('خطأ في حذف المنتج من السلة:', error);

            let statusCode = 500;
            if (error.message.includes('غير موجود')) {
                statusCode = 404;
            }

            res.status(statusCode).json({
                success: false,
                message: error.message,
                data: null
            });
        }
    }

    // API: تحديث كمية منتج في السلة
    async updateCartItem(req, res) {
        try {
            const customerId = req.customer.id;
            const cartItemId = req.params.id;
            const { quantity } = req.body;

            if (!quantity || quantity < 1) {
                return res.status(400).json({
                    success: false,
                    message: 'الكمية يجب أن تكون أكبر من صفر',
                    data: null
                });
            }

            const updatedCartItem = await CartService.updateCartItem(cartItemId, quantity, customerId);

            res.json({
                success: true,
                message: 'تم تحديث الكمية بنجاح',
                data: {
                    cartItem: {
                        id: updatedCartItem.id,
                        quantity: updatedCartItem.quantity,
                        price: parseFloat(updatedCartItem.price),
                        totalPrice: parseFloat(updatedCartItem.totalPrice)
                    }
                }
            });

        } catch (error) {
            console.error('خطأ في تحديث السلة:', error);

            let statusCode = 500;
            if (error.message.includes('غير موجود')) {
                statusCode = 404;
            } else if (error.message.includes('الكمية') || error.message.includes('متاحة')) {
                statusCode = 400;
            }

            res.status(statusCode).json({
                success: false,
                message: error.message,
                data: null
            });
        }
    }

    // API: مسح السلة بالكامل
    async clearCart(req, res) {
        try {
            const customerId = req.customer.id;
            await CartService.clearCart(customerId);

            res.json({
                success: true,
                message: 'تم مسح السلة بنجاح',
                data: null
            });

        } catch (error) {
            console.error('خطأ في مسح السلة:', error);
            res.status(500).json({
                success: false,
                message: error.message || 'حدث خطأ في الخادم',
                data: null
            });
        }
    }
}

module.exports = new CustomersController();
