'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    try {
      // محاولة حذف جميع القيود المحتملة على حقل barcode
      await queryInterface.sequelize.query(`
        DECLARE @sql NVARCHAR(MAX) = '';
        SELECT @sql = @sql + 'ALTER TABLE Products DROP CONSTRAINT ' + QUOTENAME(name) + ';' + CHAR(13)
        FROM sys.key_constraints
        WHERE parent_object_id = OBJECT_ID('Products')
        AND COL_NAME(parent_object_id, parent_column_id) = 'barcode';
        EXEC sp_executesql @sql;
      `);

      // محاولة حذف الفهارس الفريدة على حقل barcode
      await queryInterface.sequelize.query(`
        DECLARE @sql NVARCHAR(MAX) = '';
        SELECT @sql = @sql + 'DROP INDEX ' + QUOTENAME(name) + ' ON Products;' + CHAR(13)
        FROM sys.indexes
        WHERE object_id = OBJECT_ID('Products')
        AND is_unique = 1
        AND name LIKE '%barcode%';
        EXEC sp_executesql @sql;
      `);
    } catch (error) {
      console.log('Error removing constraints:', error.message);
    }
  },

  async down (queryInterface, Sequelize) {
    // لا نحتاج لإعادة إضافة القيد
  }
};
