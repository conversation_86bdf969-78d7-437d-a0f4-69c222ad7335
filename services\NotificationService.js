const { Notification, Customer } = require('../models');
const logger = require('../utils/logger');

class NotificationService {
    /**
     * إنشاء إشعار جديد
     */
    static async createNotification(data) {
        try {
            const notification = await Notification.createNotification(data);
            logger.info(`Notification created: ${notification.id}`);
            return notification;
        } catch (error) {
            logger.error('Error creating notification:', error);
            throw error;
        }
    }

    /**
     * إرسال إشعار لعميل محدد
     */
    static async notifyCustomer(customerId, data) {
        try {
            const notification = await this.createNotification({
                ...data,
                customerId
            });
            
            // يمكن إضافة إرسال إشعار فوري هنا (WebSocket, Push Notification, etc.)
            await this.sendRealTimeNotification('customer', customerId, notification);
            
            return notification;
        } catch (error) {
            logger.error('Error notifying customer:', error);
            throw error;
        }
    }

    /**
     * إرسال إشعار لمتجر محدد
     */
    static async notifyStore(storeId, data) {
        try {
            const notification = await this.createNotification({
                ...data,
                storeId
            });
            
            await this.sendRealTimeNotification('store', storeId, notification);
            
            return notification;
        } catch (error) {
            logger.error('Error notifying store:', error);
            throw error;
        }
    }

    /**
     * إرسال إشعار لمشرف محدد
     */
    static async notifyAdmin(adminId, data) {
        try {
            const notification = await this.createNotification({
                ...data,
                adminId
            });
            
            await this.sendRealTimeNotification('admin', adminId, notification);
            
            return notification;
        } catch (error) {
            logger.error('Error notifying admin:', error);
            throw error;
        }
    }

    /**
     * إرسال إشعار لجميع العملاء
     */
    static async notifyAllCustomers(data) {
        try {
            const customers = await Customer.findAll({ attributes: ['id'] });
            const notifications = [];

            for (const customer of customers) {
                const notification = await this.createNotification({
                    ...data,
                    customerId: customer.id
                });
                notifications.push(notification);
            }

            // إرسال إشعارات فورية
            await this.broadcastToUserType('customer', data);

            logger.info(`Sent notifications to ${customers.length} customers`);
            return notifications;
        } catch (error) {
            logger.error('Error notifying all customers:', error);
            throw error;
        }
    }

    /**
     * إرسال إشعار لجميع المتاجر
     */
    static async notifyAllStores(data) {
        try {
            // بما أن نموذج Store غير موجود، سنعيد مصفوفة فارغة
            const notifications = [];
            logger.info('Store model not available - skipping store notifications');
            return notifications;
        } catch (error) {
            logger.error('Error notifying all stores:', error);
            throw error;
        }
    }

    /**
     * إرسال إشعار لجميع المشرفين
     */
    static async notifyAllAdmins(data) {
        try {
            // بما أن نموذج Admin غير موجود، سنعيد مصفوفة فارغة
            const notifications = [];
            logger.info('Admin model not available - skipping admin notifications');
            return notifications;
        } catch (error) {
            logger.error('Error notifying all admins:', error);
            throw error;
        }
    }

    /**
     * إرسال إشعار لجميع المستخدمين
     */
    static async notifyAll(data) {
        try {
            const [customerNotifications, storeNotifications, adminNotifications] = await Promise.all([
                this.notifyAllCustomers(data),
                this.notifyAllStores(data),
                this.notifyAllAdmins(data)
            ]);

            const totalNotifications = [
                ...customerNotifications,
                ...storeNotifications,
                ...adminNotifications
            ];

            logger.info(`Sent notifications to all users: ${totalNotifications.length} total`);
            return totalNotifications;
        } catch (error) {
            logger.error('Error notifying all users:', error);
            throw error;
        }
    }

    /**
     * إشعارات خاصة بالطلبات
     */
    static async notifyOrderCreated(orderId, customerId, storeId) {
        try {
            // إشعار للعميل
            await this.notifyCustomer(customerId, {
                title: 'تم إنشاء طلبك بنجاح',
                message: `تم إنشاء طلبك رقم ${orderId} بنجاح. سيتم معالجته قريباً.`,
                type: 'order',
                priority: 'normal',
                actionUrl: `/customers/orders/${orderId}`,
                actionText: 'عرض الطلب',
                data: { orderId, type: 'order_created' }
            });

            // إشعار للمتجر
            await this.notifyStore(storeId, {
                title: 'طلب جديد',
                message: `لديك طلب جديد رقم ${orderId} يحتاج للمعالجة.`,
                type: 'order',
                priority: 'high',
                actionUrl: `/store/orders/${orderId}`,
                actionText: 'معالجة الطلب',
                data: { orderId, customerId, type: 'new_order' }
            });

            logger.info(`Order notifications sent for order ${orderId}`);
        } catch (error) {
            logger.error('Error sending order notifications:', error);
            throw error;
        }
    }

    /**
     * إشعارات تحديث حالة الطلب
     */
    static async notifyOrderStatusUpdate(orderId, customerId, status) {
        try {
            const statusMessages = {
                'confirmed': 'تم تأكيد طلبك',
                'preparing': 'جاري تحضير طلبك',
                'ready': 'طلبك جاهز للاستلام',
                'delivered': 'تم تسليم طلبك',
                'cancelled': 'تم إلغاء طلبك'
            };

            const message = statusMessages[status] || 'تم تحديث حالة طلبك';

            await this.notifyCustomer(customerId, {
                title: 'تحديث حالة الطلب',
                message: `${message} رقم ${orderId}`,
                type: status === 'cancelled' ? 'warning' : 'info',
                priority: 'normal',
                actionUrl: `/customers/orders/${orderId}`,
                actionText: 'عرض الطلب',
                data: { orderId, status, type: 'order_status_update' }
            });

            logger.info(`Order status notification sent for order ${orderId}: ${status}`);
        } catch (error) {
            logger.error('Error sending order status notification:', error);
            throw error;
        }
    }

    /**
     * إشعارات العروض والخصومات
     */
    static async notifyPromotion(data) {
        try {
            const promotionData = {
                title: data.title || 'عرض خاص',
                message: data.message,
                type: 'promotion',
                priority: data.priority || 'normal',
                actionUrl: data.actionUrl,
                actionText: data.actionText || 'عرض التفاصيل',
                expiresAt: data.expiresAt,
                data: { type: 'promotion', ...data.data }
            };

            if (data.targetType === 'all') {
                return await this.notifyAll(promotionData);
            } else if (data.targetType === 'customers') {
                return await this.notifyAllCustomers(promotionData);
            } else if (data.targetType === 'stores') {
                return await this.notifyAllStores(promotionData);
            } else if (data.customerId) {
                return await this.notifyCustomer(data.customerId, promotionData);
            } else if (data.storeId) {
                return await this.notifyStore(data.storeId, promotionData);
            }

            logger.info('Promotion notification sent');
        } catch (error) {
            logger.error('Error sending promotion notification:', error);
            throw error;
        }
    }

    /**
     * إرسال إشعار فوري (WebSocket/Server-Sent Events)
     * يمكن تطوير هذه الوظيفة لاحقاً
     */
    static async sendRealTimeNotification(userType, userId, notification) {
        try {
            // هنا يمكن إضافة منطق إرسال الإشعارات الفورية
            // مثل WebSocket أو Server-Sent Events
            
            logger.info(`Real-time notification sent to ${userType}:${userId}`);
        } catch (error) {
            logger.error('Error sending real-time notification:', error);
        }
    }

    /**
     * بث إشعار لنوع مستخدم معين
     */
    static async broadcastToUserType(userType, data) {
        try {
            // هنا يمكن إضافة منطق البث للمستخدمين المتصلين
            logger.info(`Broadcasting notification to all ${userType}s`);
        } catch (error) {
            logger.error('Error broadcasting notification:', error);
        }
    }

    /**
     * تنظيف الإشعارات المنتهية الصلاحية
     */
    static async cleanupExpiredNotifications() {
        try {
            const result = await Notification.destroy({
                where: {
                    expiresAt: {
                        [require('sequelize').Op.lt]: new Date()
                    }
                }
            });

            logger.info(`Cleaned up ${result} expired notifications`);
            return result;
        } catch (error) {
            logger.error('Error cleaning up expired notifications:', error);
            throw error;
        }
    }

    /**
     * إحصائيات الإشعارات
     */
    static async getNotificationStats() {
        try {
            const stats = await Notification.findAll({
                attributes: [
                    'type',
                    [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count'],
                    [require('sequelize').fn('COUNT', require('sequelize').col('readAt')), 'readCount']
                ],
                group: ['type'],
                raw: true
            });

            return stats;
        } catch (error) {
            logger.error('Error getting notification stats:', error);
            throw error;
        }
    }
}

module.exports = NotificationService;
