const { DeliveryPerson, Delivery } = require('../models');
const path = require('path');
// عرض جميع السائقين  
exports.index = async (req, res) => {
  const limit = 20;
  const currentPage = parseInt(req.query.page) || 1;
  const offset = (currentPage - 1) * limit;

  try {
    const { count, rows: deliveryPeople } = await DeliveryPerson.findAndCountAll({
      include: [{ model: Delivery, as: 'deliveries' }],
      limit,
      offset,
      order: [['createdAt', 'DESC']]
    });

    const totalPages = Math.ceil(count / limit);

    res.render('admin/drivers/index', {
      deliveryPeople,
      currentPage,
      totalPages
    });
  } catch (error) {
    console.error("Error fetching delivery people:", error);
    res.status(500).render('error', {
      error: { message: 'Unable to fetch delivery people' }
    });
  }
};

// عرض صفحة إضافة سائق
exports.createForm = (req, res) => {
  res.render('admin/drivers/create');
};

// إنشاء سائق جديد
exports.create = async (req, res) => {
  const { name, phoneNumber } = req.body;
console.log(req.file);
      let imagePath = null;
      // رفع الصور الجديدة
      if (req.file) {
          imagePath = `/uploads/deliveries/${req.file.filename}`;
      }

  try {
    await DeliveryPerson.create({
      name,
      phoneNumber,
      image : imagePath,
    });

    res.redirect(`/admin/drivers`);
  } catch (error) {
    console.error(error);
    return res.status(400).render('admin/drivers/create', {
        error: 'حدث خطأ أثناء إضافة السائق' });
  }
};
// عرض صفحة تعديل سائق
exports.editForm = async (req, res) => {
  const deliveryPerson = await DeliveryPerson.findByPk(req.params.id);
  res.render('admin/drivers/edit', { deliveryPerson });
};


exports.update = async (req, res) => {
  try {
  const { name, phoneNumber, status } = req.body;
  const { id } = req.params;
  let updates = { name, phoneNumber, status };

   const deliveryPerson = await DeliveryPerson.findByPk(req.params.id);
            
    if (!deliveryPerson) {
        return res.status(404).render('error', { error: { message: 'السائق غير موجودة' } });
    }

  // معالجة رفع الصورة الجديدة
  let imagePath = deliveryPerson.image;
  if (req.file) {
      imagePath = `/uploads/deliveries/${req.file.filename}`;
  }

  await deliveryPerson.update({
      name,
      phoneNumber,
      image : imagePath,
    });
  res.redirect(`/admin/drivers`);
   } catch (error) {
            console.error('Error updating driver:', error);
            res.status(500).render('admin/deliveries/edit', {
                error: 'حدث خطأ أثناء تحديث السائق',
                deliveryPerson: { ...req.body, id: req.params.id }
            });
        }
};

exports.delete = async (req, res) => {
  const { id } = req.params;

  try {
    await DeliveryPerson.destroy({ where: { id } });
    res.redirect('/admin/drivers');
  } catch (error) {
    console.error('خطأ أثناء حذف السائق:', error);
    res.status(500).send('حدث خطأ أثناء حذف السائق');
  }
};